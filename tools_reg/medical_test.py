import requests
from tools_reg.emp_jwt_gen import *


# Employee details
empcode = "10172959"
empcode = "182736"


from packages import *

SSO_BASE_URL = os.getenv("SSO_BASE_URL")
DEFAULT_TIMEOUT=int(os.getenv("DEFAULT_TIMEOUT"))


def medical_api(empcode):
    
    
    import requests
    try:

        GET_ENCRYPTED_PARAMS_URL = f"{SSO_BASE_URL}/get_encrypted_params"
        GET_LEAVE_DATA_URL = f"{SSO_BASE_URL}/getMedicalInsuranceDataEncrypted"
        
        # "http://192.168.13.129:2720/ssoNodeAPI/sso/getLeaveRelatedDataEncrypted"

        from_date = "2025-01-01"
        to_date = "2025-12-31"
        # Step 1: Get encryptedParams
        payload = {
            "empcode": empcode,
            "from_date": from_date,
            "to_date": to_date
        }

        headers = {"Content-Type": "application/json"}

        try:
            ##print("Fetching encrypted params...")
            res1 = requests.post(GET_ENCRYPTED_PARAMS_URL, json=payload, headers=headers)
            res1.raise_for_status()
            res1_json = res1.json()

            if res1_json.get("errorcode") != 0 or "encryptedParams" not in res1_json:
                ##print("Failed to get encrypted params:", res1_json)
                exit(1)

            encrypted_params = res1_json["encryptedParams"]
            
            token_result = jwt_token_gen(empcode)

            if not token_result["success"]:
                return {"valid": False, "data": None, "error": token_result["error"]}
            
            jwt_token = token_result["token"]

            print("Encrypted params received.")

            # Step 2: Get leave-related data using encryptedParams
            print("Fetching leave data...")
            res2 = requests.post(GET_LEAVE_DATA_URL, 
                                json={"params": encrypted_params}, 
                                headers={"Authorization": f"Bearer {jwt_token}"},
                                timeout=DEFAULT_TIMEOUT
                                             )
        except Exception as ex:
            print(ex,"ex")
            pl_summary_input="not found"
        res2.raise_for_status()
        res2_json = res2.json()
        print("Response Body:", res2_json)
        



        if res2_json["errorCode"]== 0:
            print("**********************************")
            pl_data = res2_json["data"]#["original_leave_data"].get("PL")
            if pl_data:
                pl_summary_input=pl_data
                # pl_summary_input=""
                # for i in pl_data:
                # ##print("\n--- PL Leave Data ---")
                # # for k, v in pl_data.items():
                #     ##print(f"{k}: {v}")
                #     pl_summary_input = "\n".join([f"{k}: {v}" for k, v in i.items()])
                #     #print(pl_summary_input)

            else:
                pl_summary_input="not found"
                print("PL data not found in response.")
        else:
            pl_summary_input="not found"
            print("Failed to get leave data:", res2_json.get("error", {}).get("msg"))

    except requests.RequestException as e:
        pl_summary_input="not found"
        print("HTTP Request failed:", str(e))
    except Exception as ex:
        pl_summary_input="not found"
        print("Unexpected error:", str(ex))
    return pl_summary_input
