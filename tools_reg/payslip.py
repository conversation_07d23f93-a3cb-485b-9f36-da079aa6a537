import requests
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Any
from packages import *
from tools_reg.emp_jwt_gen import *

BASE_URL = os.getenv("SSO_BASE_URL")
DEFAULT_TIMEOUT=int(os.getenv("DEFAULT_TIMEOUT"))

def get_encrypted_params(empcode: str, month: str, email_mode: str) -> str:
    """Call API to get encrypted params."""
    payload = {"empcode": empcode, "month": month, "receivingMethod": email_mode}
    resp = requests.post(f"{BASE_URL}/get_encrypted_params", json=payload)
    resp.raise_for_status()
    return resp.json().get("encryptedParams")

def get_attendance_data(empcode:str,encrypted_params: str) -> Dict[str, Any]:
    """Call API to fetch attendance data."""
    payload = {"params": encrypted_params}
    
    token_result = jwt_token_gen(empcode)

    if not token_result["success"]:
        return {"valid": False, "data": None, "error": token_result["error"]}
    
    jwt_token = token_result["token"]

    # print(111111111111111,payload)

    resp = requests.post(f"{BASE_URL}/sendPaySlipDocument",
                            headers={"Authorization": f"Bearer {jwt_token}"},
                            json=payload,
                            timeout=DEFAULT_TIMEOUT)
    # http://**************:2720/ssoNodeAPI/sso/sendPaySlipDocument
    # resp.raise_for_status()
    data = resp.json()
    print("payslip response:", data)
     # Explicit error handling (even if HTTP 200)
    if isinstance(data, dict) and data.get("errorCode") == 1:
        return {"error": data.get("message", "Unknown error from payslip service")}
    
        # raise ValueError(data.get("message", "Unknown error from payslip API"))
    return data
   

def send_payslip_1(empcode: str, month: str, email_mode: str):
    """Fetch payslip for a single month."""
    try:
        encrypted = get_encrypted_params(empcode, month, email_mode)
        print(111111111111111,encrypted,month)
        raw_data = get_attendance_data(empcode,encrypted)
        return month, raw_data
    except Exception as e:
        return month, {"error": str(e)}

def payslip_api(empcode: str, months: List[str], email_type: str) -> Dict[str, Any]:
    print("came here_payslip_api", empcode, months, email_type)
    results = {}
    both = ["personal_mail", "official_mail"]

    if "both" in email_type.lower():
        results = {}
        for email_mode in both:
            results[email_mode] = {}
            with ThreadPoolExecutor(max_workers=len(months)) as executor:
                futures = [executor.submit(send_payslip_1, empcode, month, email_mode) for month in months]
                for future in as_completed(futures):
                    month, data = future.result()
                    results[email_mode][month] = data

    elif "perso" in email_type.lower():
        email_mode = both[0]
        results[email_mode] = {}
        with ThreadPoolExecutor(max_workers=len(months)) as executor:
            futures = [executor.submit(send_payslip_1, empcode, month, email_mode) for month in months]
            for future in as_completed(futures):
                month, data = future.result()
                results[email_mode][month] = data

    elif "offi" in email_type.lower():
        email_mode = both[1]
        results[email_mode] = {}
        with ThreadPoolExecutor(max_workers=len(months)) as executor:
            futures = [executor.submit(send_payslip_1, empcode, month, email_mode) for month in months]
            for future in as_completed(futures):
                month, data = future.result()
                results[email_mode][month] = data

    else:
        results = {"error": "For security reasons, we can send only to registered mail IDs."}

    print("all results:", results)
    return results
