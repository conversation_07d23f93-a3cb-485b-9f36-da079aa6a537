# import requests
# from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Any
from tools_reg.emp_jwt_gen import *
DEFAULT_TIMEOUT=int(os.getenv("DEFAULT_TIMEOUT"))

# from datetime import datetime
# import os
# import json
# from packages import *
# import config
# # from tools_reg.flatten_json_func import *
# # BASE_URL = os.getenv("SSO_BASE_URL")
# BASE_URL_ENCRYPT = os.getenv("SSO_BASE_URL")  # "http://192.168.13.129:2720/ssoNodeAPI/sso"
# BASE_URL_REIMBURSE = f"{BASE_URL_ENCRYPT}/reimbursement_info"


def adjust_months_list2(target_months: List[str]) -> List[str]:
    """
    Adjust month list to exclude current month and shift back by 1 month.
    Example: [2025-09] -> [2025-08]
    Example: [2025-05,2025-06,2025-07,2025-08,2025-09] -> [2025-04,2025-05,2025-06,2025-07,2025-08]
    """
    current_date = datetime.now()
    current_month = f"{current_date.year}-{current_date.month:02d}"
    
    if current_month not in  target_months:
        return target_months
       
    if not target_months:
        return target_months
    # Get current month
   
    # Parse and sort months
    month_dates = []
    for month_str in target_months:
        try:
            year, month = map(int, month_str.split('-'))
            month_dates.append((year, month))
        except ValueError:
            continue
    
    if not month_dates:
        return target_months
    
    month_dates.sort()
    
    # Shift all months back by 1
    adjusted_months = []
    for year, month in month_dates:
        if month == 1:  # January -> December of previous year
            adjusted_months.append(f"{year-1}-12")
        else:
            adjusted_months.append(f"{year}-{month-1:02d}")
    
    return adjusted_months

# def filter_api_response_by_months(api_response: Dict[str, Any], target_months: List[str]) -> Dict[str, Any]:
    
#     if "error" in api_response:
#         return api_response
    
#     filtered_response = {
#         "errorCode": api_response.get("errorCode", 0),
#         "optedFor": api_response.get("optedFor", []),
#         "reimbursementInfo": {}
#     }
    

    
#     # Process reimbursementInfo if it exists
#     if "reimbursementInfo" in api_response:
#         for category, data in api_response["reimbursementInfo"].items():
#             if isinstance(data, dict):
#                 filtered_response["reimbursementInfo"][category] = {}
                
#                 # Filter months
#                 for month in target_months:
#                     if month in data:
#                         filtered_response["reimbursementInfo"][category][month] = data[month]
    
#     return filtered_response

# def get_reimbursement_encrypted(empcode: str) -> str:
#     """Call API to get encrypted params for reimbursement."""
#     payload = {
#         "empcode": empcode,
#     }
#     resp = requests.post(f"{BASE_URL_ENCRYPT}/get_encrypted_params", json=payload)
#     resp.raise_for_status()
#     data = resp.json()
#     if data.get("errorcode") != 0:
#         raise ValueError(f"Encryption failed: {data}")
#     return data.get("encryptedParams")

# def get_reimbursement_data(encrypted_params: str) -> Dict[str, Any]:
#     """Call API to fetch reimbursement info."""
#     payload = {"params": encrypted_params}
#     resp = requests.post(BASE_URL_REIMBURSE, json=payload)
#     data = resp.json()
#     # explicit error handling
#     if isinstance(data, dict) and data.get("errorCode") == 1:
#         return {"error": "Failed to fetch reimbursement info"}
#     return data

# def send_reimbursement_1(empcode: str) -> Dict[str, Any]:
#     """Fetch complete reimbursement info for an employee."""
#     try:
#         encrypted = get_reimbursement_encrypted(empcode)
#         raw_data = get_reimbursement_data(encrypted)
        
#         return raw_data
#     except Exception as e:
#         return {"error": str(e)}

# def reimbursement_api(empcode: str, months: List[str]) -> Dict[str, Any]:

#     print(f"Fetching reimbursement info for: {empcode}, filtering for months: {months}")
    
#     try:
#         # Get complete reimbursement data
#         complete_data = send_reimbursement_1(empcode)
        
#         # Check if there was an error
#         if "error" in complete_data:
#             return complete_data
        
#         context = f"""
# Reimbursement Types and Status Codes Explanation:

# 1. FUEL REIMBURSEMENT
#    Purpose: Fuel expenses for official travel and commuting
#    Status Codes:
#    - 5: Approved (reimbursement processed and paid)
#    - 0: Available for claim (eligible to submit claim)
#    - 1: Pending (claim submitted, awaiting approval)

# 2. CONVEYANCE REIMBURSEMENT  
#    Purpose: Transportation allowance for official work
#    Status Codes:
#    - 5: Approved (reimbursement processed and paid)
#    - 0: Available for claim (eligible to submit claim)
#    - 1: Pending (claim submitted, awaiting approval)

# 3. MOBILE/INTERNET REIMBURSEMENT
#    Purpose: Mobile phone and internet connection expenses for work
#    Components: Separate mobile and internet allowances
#    Status Codes:
#    - 5: Approved (reimbursement processed and paid)
#    - 0: Available for claim (eligible to submit claim)
#    - 1: Pending (claim submitted, awaiting approval)
#    Note: Mobile and internet have separate status tracking

# 4. EDUCATION REIMBURSEMENT
#    Purpose: Training, courses, certifications, and educational expenses
#    Status Codes:
#    - 1: Pending with RM2 (first level approval pending)
#    - 2: Pending with HOD (Head of Department approval pending)
#    - 3: Pending with Admin (administrative approval pending)
#    - 9: Approved (fully approved and processed)
#    - 10: Rejected (claim rejected at any approval level)

# API Data Structure:
# - eligibility_amount: Maximum amount employee can claim for the month
# - claimed_amount: Amount already claimed/submitted by employee
# - pending_amount: Amount still available to claim (usually equals eligibility_amount)
# - request_status: Current approval status (see status codes above)
# - payout_from/payout_to: Period dates for which reimbursement is applicable
# - claimed_from/claimed_to: Actual dates for which amount was claimed
# - updated_by: User ID of person who last updated the record
# - updated_dt: Timestamp of last update
# - empname: Employee name
# - empcode: Employee ID/code

# For Mobile/Internet specifically:
# - mobile_eligibility_amount/internet_eligibility_amount: Separate limits
# - mobile_claimed_amount/internet_claimed_amount: Separate claimed amounts
# - mobile_pending_amount/internet_pending_amount: Separate pending amounts
# - request_status_mobile/request_status_internet: Separate status tracking

# Reimbursement API Response:
# \n\n\n
# """
#         months = adjust_months_list(months)
#         print(months)

#         filtered_data = filter_api_response_by_months(complete_data, months)
        
#         # flatten_json_data=flatten_json(filtered_data)
#         # print(flatten_json_data)

#         context_with_data = context + json.dumps(filtered_data)
        
        
#         # Filter data for specified months only
        
#         print(f"Successfully filtered data for months: {filtered_data}")
#         return context_with_data
        
#     except Exception as e:
#         return {"error": f"Failed to fetch/filter reimbursement data: {str(e)}"}


import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
from datetime import datetime, timedelta
import os
import json
from packages import *
import config

BASE_URL_ENCRYPT = os.getenv("SSO_BASE_URL")
BASE_URL_REIMBURSE = f"{BASE_URL_ENCRYPT}/reimbursement_info"

def get_application_status_message() -> Dict[str, Any]:
    """
    Returns current application status and eligibility information
    """
    current_date = datetime.now()
    current_day = current_date.day
    current_month = f"{current_date.year}-{current_date.month:02d}"
    
    # Calculate previous month
    if current_date.month == 1:
        prev_month = f"{current_date.year - 1}-12"
        prev_month_name = "December"
    else:
        prev_month = f"{current_date.year}-{current_date.month - 1:02d}"
        prev_month_name = datetime(current_date.year, current_date.month - 1, 1).strftime("%B")
    
    status_info = {
        "current_date": current_date.strftime("%Y-%m-%d"),
        "current_day": current_day,
        "eligible_month": prev_month,
        "eligible_month_name": prev_month_name,
        "application_window_open": current_day > 7,
        "next_opening_date": f"{current_date.year}-{current_date.month:02d}-08" if current_day <= 7 else None
    }
    
    if current_day <= 7:
        status_info["message"] = f"Application for {prev_month_name} ({prev_month}) will open on {status_info['next_opening_date']}. You can view previous months' status anytime."
    else:
        status_info["message"] = f"Application window is open for {prev_month_name} ({prev_month}). You can apply and check status."
    
    return status_info

def adjust_months_list(target_months: List[str]) -> tuple[List[str], Dict[str, Any]]:
    """
    Adjust month list based on current date and application window rules.
    Returns: (adjusted_months, status_info)
    """
    if not target_months:
        return target_months, get_application_status_message()
    
    current_date = datetime.now()
    status_info = get_application_status_message()
    print(target_months,status_info)
    # Parse and sort target months
    month_dates = []
    for month_str in target_months:
        try:
            year, month = map(int, month_str.split('-'))
            month_dates.append((year, month, month_str))
        except ValueError:
            continue
    
    if not month_dates:
        return target_months, status_info
    
    month_dates.sort()
    
    # Check if user is asking for current eligible month (last month)
    eligible_month = status_info["eligible_month"]
    asking_for_current_eligible = eligible_month in target_months
    
    if asking_for_current_eligible and not status_info["application_window_open"]:
        print("*yeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee")
        # Remove current eligible month if window not open
        target_months = [m for m in target_months if m != eligible_month]
        status_info["warning"] = f"Data for {status_info['eligible_month_name']} ({eligible_month}) not available yet. {status_info['message']}"
    
    # For historical months (before current eligible month), no adjustment needed
    # They can be viewed anytime for status checking
    historical_months = []
    
    for year, month, month_str in month_dates:
        print("****",year, month, month_str,month_dates,eligible_month)
        if month_str != eligible_month :#and :  # Not the current eligible month
            historical_months.append(month_str)
    print("22222222",historical_months)
    # If window is open, include current eligible month
    if status_info["application_window_open"] and asking_for_current_eligible:
        historical_months.append(eligible_month)
        historical_months.sort()
        print("heloo")
    print(3,historical_months)
    
    historical_months=adjust_months_list2(historical_months)
    print(3,historical_months)
    return historical_months, status_info

def filter_api_response_by_months(api_response: Dict[str, Any], target_months: List[str]) -> Dict[str, Any]:
    """Filter API response to include only specified months"""
    found_data = False

    if "error" in api_response:
        return api_response,found_data
    
    filtered_response = {
        "errorCode": api_response.get("errorCode", 0),
        "optedFor": api_response.get("optedFor", []),
        "reimbursementInfo": {}
    }
    
    if "reimbursementInfo" in api_response:
        for category, data in api_response["reimbursementInfo"].items():
            if isinstance(data, dict):
                filtered_response["reimbursementInfo"][category] = {}
                
                # Filter months
                found_data = False

                for month in target_months:
                    if month in data:
                        filtered_response["reimbursementInfo"][category][month] = data[month]
                        found_data = True
                        
                  # If no target months found, get the latest month
                if not found_data and data:
                    # Sort months and get the latest one
                    available_months = list(data.keys())
                    if available_months:
                        # Assuming months are in a sortable format (e.g., "2024-01", "January-2024", etc.)
                        latest_month = max(available_months)
                        filtered_response["reimbursementInfo"][category][latest_month] = data[latest_month]
    

    
    return filtered_response,found_data

def get_reimbursement_encrypted(empcode: str) -> str:
    """Call API to get encrypted params for reimbursement."""
    payload = {"empcode": empcode}
    resp = requests.post(f"{BASE_URL_ENCRYPT}/get_encrypted_params", json=payload)
    resp.raise_for_status()
    data = resp.json()
    if data.get("errorcode") != 0:
        raise ValueError(f"Encryption failed: {data}")
    return data.get("encryptedParams")

def get_reimbursement_data(empcode:str,encrypted_params: str) -> Dict[str, Any]:
    """Call API to fetch reimbursement info."""
    payload = {"params": encrypted_params}
    token_result = jwt_token_gen(empcode)

    if not token_result["success"]:
        return {"valid": False, "data": None, "error": token_result["error"]}
    
    jwt_token = token_result["token"]

    resp = requests.post(BASE_URL_REIMBURSE, 
                        headers={"Authorization": f"Bearer {jwt_token}"},
                        json=payload,
                        timeout=DEFAULT_TIMEOUT
                        )
    data = resp.json()
    if isinstance(data, dict) and data.get("errorCode") == 1:
        return {"error": "Failed to fetch reimbursement info"}
    return data

def send_reimbursement_1(empcode: str) -> Dict[str, Any]:
    """Fetch complete reimbursement info for an employee."""
    try:
        encrypted = get_reimbursement_encrypted(empcode)
        raw_data = get_reimbursement_data(empcode,encrypted)
        return raw_data
    except Exception as e:
        return {"error": str(e)}

def reimbursement_api(empcode: str, months: List[str]) -> Dict[str, Any]:
    """
    Main API function with enhanced date logic and user guidance
    """
    print(f"Fetching reimbursement info for: {empcode}, filtering for months: {months}")
    
    try:
        # Get application status and adjust months
        adjusted_months, status_info = adjust_months_list(months)
        
        print(f"Status Info: {status_info}")
        print(f"Adjusted months: {adjusted_months}")
        
        # Get complete reimbursement data
        complete_data = send_reimbursement_1(empcode)
        
        if "error" in complete_data:
            return complete_data
        
        # Create context with status information
# Current Application Status: {status_info['message']}

        context = f"""




Reimbursement Types and Status Codes Explanation:

1. FUEL REIMBURSEMENT
   Purpose: Fuel expenses for official travel and commuting
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)

2. CONVEYANCE REIMBURSEMENT  
   Purpose: Transportation allowance for official work
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)

3. MOBILE/INTERNET REIMBURSEMENT
   Purpose: Mobile phone and internet connection expenses for work
   Components: Separate mobile and internet allowances
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)
   Note: Mobile and internet have separate status tracking

4. EDUCATION REIMBURSEMENT
   Purpose: Training, courses, certifications, and educational expenses
   Status Codes:
   - 1: Pending with RM2 (first level approval pending)
   - 2: Pending with HOD (Head of Department approval pending)
   - 3: Pending with Admin (administrative approval pending)
   - 9: Approved (fully approved and processed)
   - 10: Rejected (claim rejected at any approval level)


Important Notes:

- Applications open after 7th of each month for the previous months

- Historical data can be viewed anytime for status checking

- Latest eligible month data appears only after application window opens

## CRITICAL RULES
1. Match the employee's query type with the correct reimbursement type in the API response.
   - If the user asks about "conveyance", show only conveyance data.
   - If the user asks about "fuel", show only fuel data.
   - If there is no data for that type, clearly say so: 
     "You don’t have a conveyance claim for July. However, you do have a fuel claim approved."
2. Never confuse one type with another (fuel ≠ conveyance ≠ mobile/internet ≠ LTA ≠ education).
3. Never display internal IDs, codes, or raw JSON to the user.
4. Use plain English in responses:
   - Say "approved", "pending", "available to claim", "rejected"
   - Mention amounts in ₹ (e.g., ₹12,500)
5. For approved claims of past months: Payment should already be completed. If not received, advise the user to contact HR.
6. For education claims, always mention child name and school/college if available.
7. Applications open after the 7th of each month for the previous month.


Payout Processing Schedule (ONLY applies to future/current applications):
- Applications submitted between 25th of previous month to 10th of current month → Payout processed on 15th of current month
- Applications submitted between 11th to 24th of current month → Payout processed on 28th of current month


IMPORTANT: For historical months with status 5 (Approved), payment should already be completed. If payment not received for approved claims, advise user to contact HR.


API Data Structure:
- eligibility_amount: Maximum amount employee can claim for the month
 - claimed_amount: Amount already claimed/submitted by employee
 - pending_amount: Amount still available to claim (usually equals eligibility_amount)
 - request_status: Current approval status (see status codes above)
 - payout_from/payout_to: Period dates for which reimbursement is applicable
 - claimed_from/claimed_to: Actual dates for which amount was claimed
 - updated_by: User ID of person who last updated the record
 - updated_dt: Last update timestamp (when status was last changed)
 - empname: Employee name
 - empcode: Employee ID/code

# For Mobile/Internet specifically:
 - mobile_eligibility_amount/internet_eligibility_amount: Separate limits
 - mobile_claimed_amount/internet_claimed_amount: Separate claimed amounts
 - mobile_pending_amount/internet_pending_amount: Separate pending amounts
 - request_status_mobile/request_status_internet: Separate status tracking


Reimbursement API Response:



"""

        context=f"""
🔥 CRITICAL INSTRUCTIONS FOR AI ASSISTANT 🔥

You are a helpful employee reimbursement assistant. Provide clear, user-friendly responses without technical jargon.

REIMBURSEMENT TYPES AND STATUS CODES:

1. FUEL REIMBURSEMENT
   Purpose: Fuel expenses for official travel and commuting
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)

2. CONVEYANCE REIMBURSEMENT  
   Purpose: Transportation allowance for official work
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)

3. LTA (Leave Travel Allowance) REIMBURSEMENT
   Purpose: Travel allowance for vacation/leave travel
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)

4. MOBILE/INTERNET REIMBURSEMENT
   Purpose: Mobile phone and internet connection expenses for work
   Components: Separate mobile and internet allowances
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)
   Note: Mobile and internet have separate status tracking

5. EDUCATION REIMBURSEMENT
   Purpose: Training, courses, certifications, and educational expenses
   Status Codes:
   - 1: Pending with RM2 (first level approval pending)
   - 2: Pending with HOD (Head of Department approval pending)
   - 3: Pending with Admin (administrative approval pending)
   - 4: Pending with Admin Level 2 (second administrative approval pending)
   - 9: Approved (fully approved and processed)
   - 10: Rejected (claim rejected at any approval level)

IMPORTANT GUIDELINES:
- NEVER show status codes (0,1,2,3,4,5,9,10) to users
- Use plain English: "approved", "pending", "available to claim", "rejected"
- For approved historical claims: Payment should be completed, if not received contact HR
- Applications open after 7th of each month for previous months
- Hide technical details like IDs, codes, internal references


Payout Processing Schedule (ONLY applies to future/current applications):
- Applications submitted between 25th of previous month to 10th of current month → Payout processed on 15th of current month
- Applications submitted between 11th to 24th of current month → Payout processed on 28th of current month


IMPORTANT: For historical months with status 5 (Approved), payment should already be completed. If payment not received for approved claims, advise user to contact HR.


API Data Structure:
- eligibility_amount: Maximum amount employee can claim for the month
 - claimed_amount: Amount already claimed/submitted by employee
 - pending_amount: Amount still available to claim (usually equals eligibility_amount)
 - request_status: Current approval status (see status codes above)
 - payout_from/payout_to: Period dates for which reimbursement is applicable
 - claimed_from/claimed_to: Actual dates for which amount was claimed
 - updated_by: User ID of person who last updated the record
 - updated_dt: Last update timestamp (when status was last changed)
 - empname: Employee name
 - empcode: Employee ID/code
 - For Education: child_name, school_college_name, reimbursements details


# For Mobile/Internet specifically:
 - mobile_eligibility_amount/internet_eligibility_amount: Separate limits
 - mobile_claimed_amount/internet_claimed_amount: Separate claimed amounts
 - mobile_pending_amount/internet_pending_amount: Separate pending amounts
 - request_status_mobile/request_status_internet: Separate status tracking




RESPONSE RULES:
✅ DO:
- Use friendly, conversational language
- Translate status codes to plain English
- Mention specific amounts in rupees (₹5,000)
- Provide actionable next steps
- For education claims, mention child name and school
- If approved payment not received, suggest contacting HR

❌ DON'T:
- Show any status codes or technical IDs
- Use technical jargon
- Expose internal system details
- Show raw JSON data to users

Remember: Make reimbursement information clear and helpful for employees!

Reimbursement API Response:



    """
     
        context = """
🔥 CRITICAL INSTRUCTIONS FOR AI ASSISTANT 🔥

You are a helpful employee reimbursement assistant. Follow these rules STRICTLY:

🚨 PRIORITY RULE #1: DATA UNAVAILABILITY HANDLING 🚨
- If you see "🚨 IMPORTANT: MUST READ THIS NOTICE FIRST 🚨" or "DATA UNAVAILABILITY ALERT" - YOU MUST acknowledge this FIRST
- Start your response with: "I'm sorry, but the data for the month you requested is not available yet. Please try again later or contact HR."
- Then explain what reference data you're showing
- DO NOT treat reference data as the actual requested data

REIMBURSEMENT TYPES AND STATUS CODES:

1. FUEL REIMBURSEMENT
   Purpose: Fuel expenses for official travel and commuting
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)

2. CONVEYANCE REIMBURSEMENT  
   Purpose: Transportation allowance for official work
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)

3. LTA (Leave Travel Allowance) REIMBURSEMENT
   Purpose: Travel allowance for vacation/leave travel
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)

4. MOBILE/INTERNET REIMBURSEMENT
   Purpose: Mobile phone and internet connection expenses for work
   Components: Separate mobile and internet allowances
   Status Codes:
   - 5: Approved (reimbursement processed and paid)
   - 0: Available for claim (eligible to submit claim)
   - 1: Pending (claim submitted, awaiting approval)
   Note: Mobile and internet have separate status tracking

5. EDUCATION REIMBURSEMENT
   Purpose: Training, courses, certifications, and educational expenses
   Status Codes:
   - 1: Pending with RM2 (first level approval pending)
   - 2: Pending with HOD (Head of Department approval pending)
   - 3: Pending with Admin (administrative approval pending)
   - 4: Pending with Admin Level 2 (second administrative approval pending)
   - 9: Approved (fully approved and processed)
   - 10: Rejected (claim rejected at any approval level)

MANDATORY RESPONSE RULES:
✅ ALWAYS DO:
- **CHECK FOR DATA UNAVAILABILITY ALERTS FIRST** - If present, acknowledge immediately
- Use friendly, conversational language
- Translate status codes to plain English: "approved", "pending", "available to claim", "rejected"
- Mention specific amounts in rupees (₹5,000)
- For approved historical claims: Payment should be completed, if not received contact HR
- Answer ONLY what the user asked for - if they ask about conveyance, show only conveyance data

❌ NEVER DO:
- Ignore data unavailability notices or alerts
- Show any status codes (0,1,2,3,4,5,9,10) to users
- Use technical jargon or show raw JSON
- Mix different reimbursement types unless specifically asked

Payout Processing Schedule (ONLY applies to future/current applications):
- Applications submitted between 25th of previous month to 10th of current month → Payout processed on 15th of current month
- Applications submitted between 11th to 24th of current month → Payout processed on 28th of current month

IMPORTANT: For historical months with status 5 (Approved), payment should already be completed. If payment not received for approved claims, advise user to contact HR.

Remember: Make reimbursement information clear and helpful for employees!

Reimbursement API Response:
"""
     
#      Response Guidelines:

# - For historical approved claims (status 5): Payment should be completed, if not received contact HR

# - For current eligible month: Use current payout schedule rules

# - Don't mix historical claim processing with current month payout rules


# SECURITY & PRIVACY RULES:
# - NEVER expose internal identifiers in responses
# - NEVER show raw API data, internal IDs, or system codes
   
        # Filter data for available months
        if adjusted_months:
            filtered_data,found_data = filter_api_response_by_months(complete_data, adjusted_months)
        else:
            found_data=True
            filtered_data = {"message": "No data available for requested months", "status_info": status_info}
        
        # Add status information to response
        # if isinstance(filtered_data, dict):
        #     filtered_data["application_status"] = status_info
        print(filtered_data)
        if found_data== False:
            print("yes")
            notice_message = f"\n\nCRITICAL NOTICE: Data is not available for your specified month. What you asked for is not available yet in my scope. Please try later or contact HR.\n\nFor reference, here is the latest available data :"

            context_with_data = (context 
            + notice_message
            +json.dumps(filtered_data))
        else:

            context_with_data = context + json.dumps(filtered_data)
        
        print(f"Successfully processed data with status: {context_with_data}")
        return context_with_data
        
    except Exception as e:
        print(e)
        return {"error": f"Failed to fetch/filter reimbursement data: {str(e)}"}

# def get_user_guidance_message(months: List[str]) -> str:
#     """
#     Generate user-friendly guidance message based on requested months
#     """
#     _, status_info = adjust_months_list(months)
    
#     messages = [status_info["message"]]
    
#     if "warning" in status_info:
#         messages.append(f"⚠️ {status_info['warning']}")
    
#     if not status_info["application_window_open"]:
#         messages.append(f"💡 You can still view all previous months' reimbursement status and details.")
#         messages.append(f"📅 Next application window opens on: {status_info['next_opening_date']}")
    
#     return "\n".join(messages)

# # Example usage function for testing
# def example_usage():
#     """Example of how to use the enhanced reimbursement API"""
#     empcode = "10115606"
    
#     # Case 1: User asking for current month (August 2025) on Sep 6
#     print("=== Case 1: Asking for August 2025 on Sep 6 ===")
#     result1 = reimbursement_api(empcode, ["2025-08"])
#     print(get_user_guidance_message(["2025-08"]))
    
#     # Case 2: User asking for July 2025 (historical data)
#     print("\n=== Case 2: Asking for July 2025 ===")
#     result2 = reimbursement_api(empcode, ["2025-07"])
#     print(get_user_guidance_message(["2025-07"]))
    
#     # Case 3: Multiple months including current eligible
#     print("\n=== Case 3: Multiple months including August ===")
#     result3 = reimbursement_api(empcode, ["2025-06", "2025-07", "2025-08"])
#     print(get_user_guidance_message(["2025-06", "2025-07", "2025-08"]))