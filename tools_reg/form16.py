import requests
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Any
from packages import *
from tools_reg.emp_jwt_gen import *

BASE_URL_ENCRYPT = os.getenv("SSO_BASE_URL")
# "http://192.168.13.129:2720/ssoNodeAPI/sso"
BASE_URL_SEND = os.getenv("SSO_SEND_URL")
# "http://192.168.14.230:6798/ecarebot"
DEFAULT_TIMEOUT=int(os.getenv("DEFAULT_TIMEOUT"))

def get_form16_encrypted(empcode: str, year: str, form_type: str, email_mode: str) -> str:
    """Call API to get encrypted params for Form 16."""
    payload = {
        "empcode": empcode,
        "financial_year": year,
        "formType": form_type,           # part_a, part_b, tax_comp
        "receivingMethod": email_mode    # personal_mail / official_mail
    }
    resp = requests.post(f"{BASE_URL_ENCRYPT}/get_encrypted_params", json=payload)
    resp.raise_for_status()
    data = resp.json()
    if data.get("errorcode") != 0:
        raise ValueError(f"Encryption failed: {data}")
    return data.get("encryptedParams")

def send_form16_doc(empcode:str,encrypted_params: str) -> Dict[str, Any]:
    """Call API to send Form 16 document."""
    payload = {"params": encrypted_params}
    token_result = jwt_token_gen(empcode)

    if not token_result["success"]:
        return {"valid": False, "data": None, "error": token_result["error"]}
    
    jwt_token = token_result["token"]

    resp = requests.post(f"{BASE_URL_SEND}/sendForm16Document", 
                        headers={"Authorization": f"Bearer {jwt_token}"},

                        json=payload,
                        timeout=DEFAULT_TIMEOUT)
    data = resp.json()
    # explicit error handling
    if isinstance(data, dict) and data.get("errorCode") == 1:
        return {"error": data.get("message", "Unknown error from Form16 service")}
    return data

def send_form16_1(empcode: str, year: str, form_type: str, email_mode: str):
    """Fetch Form 16 for a single year & form_type."""
    try:
        encrypted = get_form16_encrypted(empcode, year, form_type, email_mode)
        print(f"[DEBUG] Encrypted for {year} - {form_type}: {encrypted}")
        raw_data = send_form16_doc(empcode,encrypted)
        return f"{year}_{form_type}", raw_data
    except Exception as e:
        return f"{year}_{form_type}", {"error": str(e)}

def form16_api(empcode: str, years: List[str], form_types: List[str], email_type: str) -> Dict[str, Any]:
    print("came here_form16_api", empcode, years, form_types, email_type)
    results = {}
    both = ["personal_mail", "official_mail"]

    if "both" in email_type.lower():
        for email_mode in both:
            results[email_mode] = {}
            with ThreadPoolExecutor(max_workers=len(years) * len(form_types)) as executor:
                futures = []

                # 🔹 Create one future per (year, form_type)
                for year in years:
                    for form_type in form_types:
                        future = executor.submit(send_form16_1, empcode, year, form_type, email_mode)
                        futures.append(future)

                # 🔹 Collect results
                for future in as_completed(futures):
                    key, data = future.result()
                    results[email_mode][key] = data

    elif "perso" in email_type.lower():
        email_mode = both[0]
        results[email_mode] = {}
        with ThreadPoolExecutor(max_workers=len(years) * len(form_types)) as executor:
            futures = []

            for year in years:
                for form_type in form_types:
                    future = executor.submit(send_form16_1, empcode, year, form_type, email_mode)
                    futures.append(future)

            for future in as_completed(futures):
                key, data = future.result()
                results[email_mode][key] = data

    elif "offi" in email_type.lower():
        email_mode = both[1]
        results[email_mode] = {}
        with ThreadPoolExecutor(max_workers=len(years) * len(form_types)) as executor:
            futures = []

            for year in years:
                for form_type in form_types:
                    future = executor.submit(send_form16_1, empcode, year, form_type, email_mode)
                    futures.append(future)

            for future in as_completed(futures):
                key, data = future.result()
                results[email_mode][key] = data

    else:
        results = {"error": "For security reasons, we can send only to registered mail IDs."}

    print("all results:", results)
    return results


def form16_api1(empcode: str, years: List[str], form_types: List[str], email_type: str) -> Dict[str, Any]:
    print("came here_form16_api", empcode, years, form_types, email_type)
    results = {}
    both = ["personal_mail", "official_mail"]

    if "both" in email_type.lower():
        for email_mode in both:
            results[email_mode] = {}
            with ThreadPoolExecutor(max_workers=len(years) * len(form_types)) as executor:
                futures = [
                    executor.submit(send_form16_1, empcode, year, form_type, email_mode)
                    for year in years for form_type in form_types
                ]
                for future in as_completed(futures):
                    key, data = future.result()
                    results[email_mode][key] = data

    elif "perso" in email_type.lower():
        email_mode = both[0]
        results[email_mode] = {}
        with ThreadPoolExecutor(max_workers=len(years) * len(form_types)) as executor:
            futures = [
                executor.submit(send_form16_1, empcode, year, form_type, email_mode)
                for year in years for form_type in form_types
            ]
            for future in as_completed(futures):
                key, data = future.result()
                results[email_mode][key] = data

    elif "offi" in email_type.lower():
        email_mode = both[1]
        results[email_mode] = {}
        with ThreadPoolExecutor(max_workers=len(years) * len(form_types)) as executor:
            futures = [
                executor.submit(send_form16_1, empcode, year, form_type, email_mode)
                for year in years for form_type in form_types
            ]
            for future in as_completed(futures):
                key, data = future.result()
                results[email_mode][key] = data

    else:
        results = {"error": "For security reasons, we can send only to registered mail IDs."}

    print("all results:", results)
    return results
