import requests


from packages import *
from tools_reg.emp_jwt_gen import *
DEFAULT_TIMEOUT=int(os.getenv("DEFAULT_TIMEOUT"))

SSO_BASE_URL = os.getenv("SSO_BASE_URL")

def leave_api(empcode):
    
    import requests
    try:

        
        GET_ENCRYPTED_PARAMS_URL = f"{SSO_BASE_URL}/get_encrypted_params"
        GET_LEAVE_DATA_URL = f"{SSO_BASE_URL}/getLeaveRelatedDataEncrypted"

        # from_date = "2025-01-01"
        # to_date = "2025-12-31"
        # from datetime import datetime

        # Get current date
        current_date = datetime.now()
        current_year = current_date.year

        # Set from_date to January 1st of current year
        from_date = f"{current_year}-01-01"

        # Set to_date to December 31st of current year  
        to_date = f"{current_year}-12-31"

        print(f"From: {from_date}")
        print(f"To: {to_date}")
        # Step 1: Get encryptedParams
        payload = {
            "empcode": empcode,
            "from_date": from_date,
            "to_date": to_date
        }

        headers = {"Content-Type": "application/json"}

        try:
            ##print("Fetching encrypted params...")
            res1 = requests.post(GET_ENCRYPTED_PARAMS_URL, json=payload, headers=headers)
            res1.raise_for_status()
            res1_json = res1.json()

            if res1_json.get("errorcode") != 0 or "encryptedParams" not in res1_json:
                ##print("Failed to get encrypted params:", res1_json)
                exit(1)

            encrypted_params = res1_json["encryptedParams"]
            
            token_result = jwt_token_gen(empcode)

            if not token_result["success"]:
                return {"valid": False, "data": None, "error": token_result["error"]}
            
            jwt_token = token_result["token"]

   
            ##print("Encrypted params received.")

            # Step 2: Get leave-related data using encryptedParams
            ##print("Fetching leave data...")
            res2 = requests.post(GET_LEAVE_DATA_URL, 
                                json={"params": encrypted_params}, 
                                headers={"Authorization": f"Bearer {jwt_token}"},
                                timeout=DEFAULT_TIMEOUT

                                             )
        except Exception as ex:
            pl_summary_input="not found"
        res2.raise_for_status()
        res2_json = res2.json()

        if res2_json.get("error", {}).get("code") == 0:
            pl_data = res2_json["data"]["original_leave_data"]#.get("PL")
            if pl_data:
                ##print("\n--- PL Leave Data ---")
                # for k, v in pl_data.items():
                    ##print(f"{k}: {v}")
                    
                context = f"""
Leave Types Explanation:

1. SL (Sick Leave) - leave_type_id: 1
   Purpose: Medical emergencies, illness, health-related absences
   Usage: When employee is unwell and cannot work
   Allocation: Typically 12 days per year (auto-credited)

2. PL (Privilege Leave/Casual Leave) - leave_type_id: 2
   Purpose: Personal activities, vacations, family events, and sometimes medical purposes
   Usage: Flexible leave type - can be used for various reasons including sick days when SL is exhausted
   Allocation: Typically 12 days per year (auto-credited)
   Note: In this system, PL is often used for medical reasons when SL quota is finished

3. SPECIAL (Special Leave)
   Purpose: Specific circumstances like bereavement, marriage, maternity/paternity
   Usage: Granted for special life events or emergencies

API Data Structure:
- debit: Total leave days used/taken
- credit: Leave days allocated for the period
- total_balance: Current available leave balance
- primary_balance: Main leave balance
- carry_forwarded_balance: Leave days carried from previous period
- pending: Leave requests awaiting approval
- auto_credit: Automatically credited leave days
- reason: Employee's stated reason for leave
- status: APPROVED/PENDING/DECLINED
- start_date/end_date: Leave period dates
- leave_days: Number of days requested
- applied_on: When request was submitted
- approved_on: When leave was approved
- leave_type_id: 1=SL, 2=PL, others for special leaves

Leave API Response:
"""    
                # pl_summary_input = "\n".join([f"{k}: {v}" for k, v in pl_data.items()])
               

                import json
                pl_summary_input = context + json.dumps(pl_data)                
                print("*******************************")
                print(pl_summary_input)
                print("*******************************")

            else:
                pl_summary_input="not found"
                ##print("PL data not found in response.")
        else:
            pl_summary_input="not found"
            ##print("Failed to get leave data:", res2_json.get("error", {}).get("msg"))

    except requests.RequestException as e:
        pl_summary_input="not found"
        print("HTTP Request failed:", str(e))
    except Exception as ex:
        pl_summary_input="not found"
        print("Unexpected error:", str(ex))
    return pl_summary_input
