# import requests
# from concurrent.futures import ThreadPoolExecutor, as_completed
# from typing import List, Dict, Any
# from datetime import datetime
# import os
# import json
# from packages import *
# import config
# # from tools_reg.flatten_json_func import *
# # BASE_URL = os.getenv("SSO_BASE_URL")
# BASE_URL_ENCRYPT = os.getenv("SSO_BASE_URL")  # "http://192.168.13.129:2720/ssoNodeAPI/sso"
# BASE_URL_REIMBURSE = f"{BASE_URL_ENCRYPT}/reimbursement_info"


# def adjust_months_list(target_months: List[str]) -> List[str]:
#     """
#     Adjust month list to exclude current month and shift back by 1 month.
#     Example: [2025-09] -> [2025-08]
#     Example: [2025-05,2025-06,2025-07,2025-08,2025-09] -> [2025-04,2025-05,2025-06,2025-07,2025-08]
#     """
#     current_date = datetime.now()
#     current_month = f"{current_date.year}-{current_date.month:02d}"
    
#     if current_month not in  target_months:
#         return target_months
       
#     if not target_months:
#         return target_months
#     # Get current month
   
#     # Parse and sort months
#     month_dates = []
#     for month_str in target_months:
#         try:
#             year, month = map(int, month_str.split('-'))
#             month_dates.append((year, month))
#         except ValueError:
#             continue
    
#     if not month_dates:
#         return target_months
    
#     month_dates.sort()
    
#     # Shift all months back by 1
#     adjusted_months = []
#     for year, month in month_dates:
#         if month == 1:  # January -> December of previous year
#             adjusted_months.append(f"{year-1}-12")
#         else:
#             adjusted_months.append(f"{year}-{month-1:02d}")
    
#     return adjusted_months

# def filter_api_response_by_months(api_response: Dict[str, Any], target_months: List[str]) -> Dict[str, Any]:
    
#     if "error" in api_response:
#         return api_response
    
#     filtered_response = {
#         "errorCode": api_response.get("errorCode", 0),
#         "optedFor": api_response.get("optedFor", []),
#         "reimbursementInfo": {}
#     }
    

    
#     # Process reimbursementInfo if it exists
#     if "reimbursementInfo" in api_response:
#         for category, data in api_response["reimbursementInfo"].items():
#             if isinstance(data, dict):
#                 filtered_response["reimbursementInfo"][category] = {}
                
#                 # Filter months
#                 for month in target_months:
#                     if month in data:
#                         filtered_response["reimbursementInfo"][category][month] = data[month]
    
#     return filtered_response

# def get_reimbursement_encrypted(empcode: str) -> str:
#     """Call API to get encrypted params for reimbursement."""
#     payload = {
#         "empcode": empcode,
#     }
#     resp = requests.post(f"{BASE_URL_ENCRYPT}/get_encrypted_params", json=payload)
#     resp.raise_for_status()
#     data = resp.json()
#     if data.get("errorcode") != 0:
#         raise ValueError(f"Encryption failed: {data}")
#     return data.get("encryptedParams")

# def get_reimbursement_data(encrypted_params: str) -> Dict[str, Any]:
#     """Call API to fetch reimbursement info."""
#     payload = {"params": encrypted_params}
#     resp = requests.post(BASE_URL_REIMBURSE, json=payload)
#     data = resp.json()
#     # explicit error handling
#     if isinstance(data, dict) and data.get("errorCode") == 1:
#         return {"error": "Failed to fetch reimbursement info"}
#     return data

# def send_reimbursement_1(empcode: str) -> Dict[str, Any]:
#     """Fetch complete reimbursement info for an employee."""
#     try:
#         encrypted = get_reimbursement_encrypted(empcode)
#         raw_data = get_reimbursement_data(encrypted)
        
#         return raw_data
#     except Exception as e:
#         return {"error": str(e)}

# def reimbursement_api(empcode: str, months: List[str]) -> Dict[str, Any]:

#     print(f"Fetching reimbursement info for: {empcode}, filtering for months: {months}")
    
#     try:
#         # Get complete reimbursement data
#         complete_data = send_reimbursement_1(empcode)
        
#         # Check if there was an error
#         if "error" in complete_data:
#             return complete_data
        
#         context = f"""
# Reimbursement Types and Status Codes Explanation:

# 1. FUEL REIMBURSEMENT
#    Purpose: Fuel expenses for official travel and commuting
#    Status Codes:
#    - 5: Approved (reimbursement processed and paid)
#    - 0: Available for claim (eligible to submit claim)
#    - 1: Pending (claim submitted, awaiting approval)

# 2. CONVEYANCE REIMBURSEMENT  
#    Purpose: Transportation allowance for official work
#    Status Codes:
#    - 5: Approved (reimbursement processed and paid)
#    - 0: Available for claim (eligible to submit claim)
#    - 1: Pending (claim submitted, awaiting approval)

# 3. MOBILE/INTERNET REIMBURSEMENT
#    Purpose: Mobile phone and internet connection expenses for work
#    Components: Separate mobile and internet allowances
#    Status Codes:
#    - 5: Approved (reimbursement processed and paid)
#    - 0: Available for claim (eligible to submit claim)
#    - 1: Pending (claim submitted, awaiting approval)
#    Note: Mobile and internet have separate status tracking

# 4. EDUCATION REIMBURSEMENT
#    Purpose: Training, courses, certifications, and educational expenses
#    Status Codes:
#    - 1: Pending with RM2 (first level approval pending)
#    - 2: Pending with HOD (Head of Department approval pending)
#    - 3: Pending with Admin (administrative approval pending)
#    - 9: Approved (fully approved and processed)
#    - 10: Rejected (claim rejected at any approval level)

# API Data Structure:
# - eligibility_amount: Maximum amount employee can claim for the month
# - claimed_amount: Amount already claimed/submitted by employee
# - pending_amount: Amount still available to claim (usually equals eligibility_amount)
# - request_status: Current approval status (see status codes above)
# - payout_from/payout_to: Period dates for which reimbursement is applicable
# - claimed_from/claimed_to: Actual dates for which amount was claimed
# - updated_by: User ID of person who last updated the record
# - updated_dt: Timestamp of last update
# - empname: Employee name
# - empcode: Employee ID/code

# For Mobile/Internet specifically:
# - mobile_eligibility_amount/internet_eligibility_amount: Separate limits
# - mobile_claimed_amount/internet_claimed_amount: Separate claimed amounts
# - mobile_pending_amount/internet_pending_amount: Separate pending amounts
# - request_status_mobile/request_status_internet: Separate status tracking

# Reimbursement API Response:
# \n\n\n
# """
#         months = adjust_months_list(months)
#         print(months)

#         filtered_data = filter_api_response_by_months(complete_data, months)
        
#         # flatten_json_data=flatten_json(filtered_data)
#         # print(flatten_json_data)

#         context_with_data = context + json.dumps(filtered_data)
        
        
#         # Filter data for specified months only
        
#         print(f"Successfully filtered data for months: {filtered_data}")
#         return context_with_data
        
#     except Exception as e:
#         return {"error": f"Failed to fetch/filter reimbursement data: {str(e)}"}
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Tuple
from datetime import datetime, timedelta
import os
import json
from packages import *
import config

BASE_URL_ENCRYPT = os.getenv("SSO_BASE_URL")
BASE_URL_REIMBURSE = f"{BASE_URL_ENCRYPT}/reimbursement_info"


def get_reimbursement_eligibility_info() -> Dict[str, Any]:
    """
    Returns information about reimbursement eligibility rules and current status.
    """
    current_date = datetime.now()
    current_day = current_date.day
    current_month_str = f"{current_date.year}-{current_date.month:02d}"
    
    # Calculate previous month
    if current_date.month == 1:
        prev_month = datetime(current_date.year - 1, 12, 1)
    else:
        prev_month = datetime(current_date.year, current_date.month - 1, 1)
    
    prev_month_str = f"{prev_month.year}-{prev_month.month:02d}"
    
    # Calculate two months back properly
    if current_date.month <= 2:
        # January or February - go to previous year
        if current_date.month == 1:  # January
            two_months_back = datetime(current_date.year - 1, 11, 1)  # November previous year
        else:  # February  
            two_months_back = datetime(current_date.year - 1, 12, 1)  # December previous year
    else:
        two_months_back = datetime(current_date.year, current_date.month - 2, 1)
    
    two_months_back_str = f"{two_months_back.year}-{two_months_back.month:02d}"
    
    eligibility_info = {
        "current_date": current_date.strftime("%Y-%m-%d"),
        "current_day": current_day,
        "reimbursement_rule": {
            "cutoff_date": 7,
            "explanation": "Reimbursement applications can only be submitted after the 7th of each month"
        }
    }
    
    if current_day >= 7:
        # After 7th - can apply for previous month's reimbursement
        eligibility_info.update({
            "status": "ELIGIBLE_TO_APPLY",
            "eligible_month": prev_month_str,
            "message": f"✅ You can now apply for {prev_month.strftime('%B %Y')} reimbursement",
            "detailed_explanation": f"Since today is {current_date.strftime('%B %d, %Y')} (after 7th), you are eligible to apply for {prev_month.strftime('%B %Y')} reimbursement. The system will show data for {prev_month_str}.",
            "available_months": [prev_month_str],
            "next_eligibility": {
                "date": f"{current_date.year}-{current_date.month:02d}-07" if current_date.month < 12 else f"{current_date.year + 1}-01-07",
                "for_month": current_month_str,
                "message": f"Next month after 7th, you can apply for {current_date.strftime('%B %Y')} reimbursement"
            }
        })
    else:
        # Before 7th - can only apply for two months back
        eligibility_info.update({
            "status": "NOT_YET_ELIGIBLE",
            "eligible_month": two_months_back_str,
            "message": f"❌ Too early to apply for {prev_month.strftime('%B %Y')} reimbursement",
            "detailed_explanation": f"Since today is {current_date.strftime('%B %d, %Y')} (before 7th), you cannot yet apply for {prev_month.strftime('%B %Y')} reimbursement. You can only apply for {two_months_back.strftime('%B %Y')} or earlier months. Wait until {current_date.strftime('%B')} 7th to apply for {prev_month.strftime('%B %Y')}.",
            "available_months": [two_months_back_str],
            "wait_until": f"{current_date.year}-{current_date.month:02d}-07",
            "days_to_wait": 7 - current_day,
            "next_eligibility": {
                "date": f"{current_date.year}-{current_date.month:02d}-07",
                "for_month": prev_month_str,
                "message": f"Wait {7 - current_day} more days to apply for {prev_month.strftime('%B %Y')} reimbursement"
            }
        })
    
    return eligibility_info


def adjust_months_list_with_rules(target_months: List[str]) -> Tuple[List[str], Dict[str, Any]]:
    """
    Adjust month list based on the 7th date rule and return eligibility info.
    
    Rules:
    - After 7th of current month: Can apply for previous month reimbursement
    - Before 7th of current month: Can only apply for two months back or earlier
    
    Returns:
        Tuple of (adjusted_months_list, eligibility_info)
    """
    eligibility_info = get_reimbursement_eligibility_info()
    current_date = datetime.now()
    current_day = current_date.day
    
    # If no target months specified, use eligible months
    if not target_months:
        return eligibility_info["available_months"], eligibility_info
    
    # Parse target months
    valid_months = []
    invalid_months = []
    
    for month_str in target_months:
        try:
            year, month = map(int, month_str.split('-'))
            month_date = datetime(year, month, 1)
            
            # Check if month is eligible based on current date rules
            if current_day >= 7:
                # Can apply for previous month or earlier
                prev_month = datetime(current_date.year, current_date.month - 1, 1) if current_date.month > 1 else datetime(current_date.year - 1, 12, 1)
                if month_date <= prev_month:
                    valid_months.append(month_str)
                else:
                    invalid_months.append(month_str)
            else:
                # Can only apply for two months back or earlier
                two_months_back = current_date.replace(day=1) - timedelta(days=32)
                two_months_back = two_months_back.replace(day=1) - timedelta(days=1)
                two_months_back = two_months_back.replace(day=1)
                
                if month_date <= two_months_back:
                    valid_months.append(month_str)
                else:
                    invalid_months.append(month_str)
                    
        except ValueError:
            invalid_months.append(month_str)
    
    # Add information about invalid months
    if invalid_months:
        eligibility_info["invalid_months"] = invalid_months
        eligibility_info["invalid_reason"] = "These months are not yet eligible for reimbursement application based on the 7th date rule"
    
    return sorted(valid_months), eligibility_info


def filter_api_response_by_months(api_response: Dict[str, Any], target_months: List[str]) -> Dict[str, Any]:
    """Filter API response to include only specified months."""
    if "error" in api_response:
        return api_response
    
    filtered_response = {
        "errorCode": api_response.get("errorCode", 0),
        "optedFor": api_response.get("optedFor", []),
        "reimbursementInfo": {}
    }
    
    # Process reimbursementInfo if it exists
    if "reimbursementInfo" in api_response:
        for category, data in api_response["reimbursementInfo"].items():
            if isinstance(data, dict):
                filtered_response["reimbursementInfo"][category] = {}
                
                # Filter months
                for month in target_months:
                    if month in data:
                        filtered_response["reimbursementInfo"][category][month] = data[month]
    
    return filtered_response


def get_reimbursement_encrypted(empcode: str) -> str:
    """Call API to get encrypted params for reimbursement."""
    payload = {"empcode": empcode}
    resp = requests.post(f"{BASE_URL_ENCRYPT}/get_encrypted_params", json=payload)
    resp.raise_for_status()
    data = resp.json()
    if data.get("errorcode") != 0:
        raise ValueError(f"Encryption failed: {data}")
    return data.get("encryptedParams")


def get_reimbursement_data(encrypted_params: str) -> Dict[str, Any]:
    """Call API to fetch reimbursement info."""
    payload = {"params": encrypted_params}
    resp = requests.post(BASE_URL_REIMBURSE, json=payload)
    data = resp.json()
    
    # Explicit error handling
    if isinstance(data, dict) and data.get("errorCode") == 1:
        return {"error": "Failed to fetch reimbursement info"}
    return data


def send_reimbursement_1(empcode: str) -> Dict[str, Any]:
    """Fetch complete reimbursement info for an employee."""
    try:
        encrypted = get_reimbursement_encrypted(empcode)
        raw_data = get_reimbursement_data(encrypted)
        return raw_data
    except Exception as e:
        return {"error": str(e)}


def reimbursement_api(empcode: str, months: List[str] = None) -> Dict[str, Any]:
    """
    Enhanced reimbursement API with 7th date rule enforcement.
    
    Args:
        empcode: Employee code
        months: List of months to fetch (optional, will use eligible months if not provided)
    
    Returns:
        Dict containing reimbursement data with eligibility information
    """
    # Get eligibility information and adjust months based on rules
    adjusted_months, eligibility_info = adjust_months_list_with_rules(months or [])
    
    print(f"\n🔍 REIMBURSEMENT ELIGIBILITY CHECK")
    print(f"📅 Current Date: {eligibility_info['current_date']},{eligibility_info}")
    print(f"📋 Status: {eligibility_info['status']}")
    print(f"💬 {eligibility_info['message']}")
    print(f"📝 {eligibility_info['detailed_explanation']}")
    
    if eligibility_info.get('invalid_months'):
        print(f"❌ Invalid months requested: {eligibility_info['invalid_months']}")
        print(f"🚫 Reason: {eligibility_info['invalid_reason']}")
    
    print(f"✅ Fetching data for eligible months: {adjusted_months}")
    print(f"👤 Employee: {empcode}")
    print("-" * 60)
    
    try:
        # Get complete reimbursement data
        complete_data = send_reimbursement_1(empcode)
        
        # Check if there was an error
        if "error" in complete_data:
            return {
                "error": complete_data["error"],
                "eligibility_info": eligibility_info
            }
        
        # Filter data for eligible months only
        filtered_data = filter_api_response_by_months(complete_data, adjusted_months)
        
        # Enhanced context with rules explanation
        context = f"""
🔄 REIMBURSEMENT SYSTEM - RULES & DATA

📋 IMPORTANT REIMBURSEMENT RULES:
═══════════════════════════════════
🗓️  7th DATE RULE: You can only apply for reimbursement AFTER the 7th of each month
📅 Current Date: {eligibility_info['current_date']}
🎯 Status: {eligibility_info['status']}
💡 {eligibility_info['detailed_explanation']}

{f"⏳ Next Eligibility: {eligibility_info['next_eligibility']['message']}" if 'next_eligibility' in eligibility_info else ""}

📊 REIMBURSEMENT TYPES & STATUS CODES:
═════════════════════════════════════

1️⃣ FUEL REIMBURSEMENT
   Purpose: Fuel expenses for official travel and commuting
   Status Codes:
   • 5: ✅ Approved (reimbursement processed and paid)
   • 0: 📝 Available for claim (eligible to submit claim)
   • 1: ⏳ Pending (claim submitted, awaiting approval)

2️⃣ CONVEYANCE REIMBURSEMENT  
   Purpose: Transportation allowance for official work
   Status Codes:
   • 5: ✅ Approved (reimbursement processed and paid)
   • 0: 📝 Available for claim (eligible to submit claim)
   • 1: ⏳ Pending (claim submitted, awaiting approval)

3️⃣ MOBILE/INTERNET REIMBURSEMENT
   Purpose: Mobile phone and internet connection expenses for work
   Components: Separate mobile and internet allowances
   Status Codes:
   • 5: ✅ Approved (reimbursement processed and paid)
   • 0: 📝 Available for claim (eligible to submit claim)
   • 1: ⏳ Pending (claim submitted, awaiting approval)
   Note: Mobile and internet have separate status tracking

4️⃣ EDUCATION REIMBURSEMENT
   Purpose: Training, courses, certifications, and educational expenses
   Status Codes:
   • 1: 🔄 Pending with RM2 (first level approval pending)
   • 2: 🔄 Pending with HOD (Head of Department approval pending)
   • 3: 🔄 Pending with Admin (administrative approval pending)
   • 9: ✅ Approved (fully approved and processed)
   • 10: ❌ Rejected (claim rejected at any approval level)

📈 DATA FIELD EXPLANATIONS:
══════════════════════════
• eligibility_amount: Maximum amount you can claim for the month
• claimed_amount: Amount already claimed/submitted by you
• pending_amount: Amount still available to claim
• request_status: Current approval status (see codes above)
• payout_from/payout_to: Period dates for reimbursement
• claimed_from/claimed_to: Actual claim period dates
• updated_by: Last person who updated the record
• updated_dt: Last update timestamp

🎯 CURRENT REQUEST RESULTS:
═══════════════════════════
Months Processed: {adjusted_months}
Employee Code: {empcode}

📊 REIMBURSEMENT DATA:
═════════════════════
"""
        
        # Prepare final response
        response_data = {
            "eligibility_info": eligibility_info,
            "processed_months": adjusted_months,
            "reimbursement_data": filtered_data,
            "context_explanation": context + json.dumps(filtered_data, indent=2)
        }
        
        print(f"✅ Successfully processed reimbursement data")
        print(f"📊 Data available for months: {adjusted_months}")
        
        return response_data
        
    except Exception as e:
        error_response = {
            "error": f"Failed to fetch/filter reimbursement data: {str(e)}",
            "eligibility_info": eligibility_info
        }
        print(f"❌ Error: {str(e)}")
        return error_response