from packages import *
from tools_reg.emp_jwt_gen import *
SSO_BASE_URL1 = os.getenv("SSO_BASE_URL1")
SSO_BASE_URL2 = os.getenv("SSO_BASE_URL2")

def jwt_token_gen(emp_id: str) -> dict:
    """
    Generate JWT token for employee authentication
    
    Args:
        emp_id (str): Employee ID to generate token for
        
    Returns:
        dict: Contains success status, token, or error information
    """
    try:
        token_resp = requests.post(
            f"{SSO_BASE_URL1}/generate_token",
            headers={"Content-Type": "application/json"},
            json={"user": {"searchKeyword": emp_id}},
            timeout=10
        )
        token_resp.raise_for_status()
        token_json = token_resp.json()
        
        if token_json.get("errorcode") != 0 or "jwt_token" not in token_json:
            return {"success": False, "token": None, "error": "Failed to generate token"}
        
        return {"success": True, "token": token_json["jwt_token"], "error": None}
        
    except requests.RequestException as e:
        return {"success": False, "token": None, "error": f"Request error: {str(e)}"}
    except Exception as e:
        return {"success": False, "token": None, "error": f"Unexpected error: {str(e)}"}
