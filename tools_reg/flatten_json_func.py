import json

def flatten_json(data, parent_key='', sep='_'):
    """
    Flattens a nested JSON object into a single-level dictionary.

    Args:
        data (dict): The nested JSON object (Python dictionary).
        parent_key (str): The prefix for the flattened keys.
        sep (str): The separator used to join parent and child keys.

    Returns:
        dict: The flattened JSON object.
    """
    items = {}
    for k, v in data.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.update(flatten_json(v, new_key, sep=sep))
        elif isinstance(v, list):
            for i, item in enumerate(v):
                if isinstance(item, dict):
                    items.update(flatten_json(item, f"{new_key}{sep}{i}", sep=sep))
                else:
                    items[f"{new_key}{sep}{i}"] = item
        else:
            items[new_key] = v
    return items