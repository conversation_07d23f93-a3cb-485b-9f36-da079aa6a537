import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
from datetime import datetime
from packages import *

# BASE_URL = os.getenv("SSO_BASE_URL")
BASE_URL_ENCRYPT = os.getenv("SSO_BASE_URL")#"http://192.168.13.129:2720/ssoNodeAPI/sso"
BASE_URL_REIMBURSE = f"{BASE_URL_ENCRYPT}/reimbursement_info"

def get_reimbursement_encrypted(empcode: str, month: str) -> str:
    """Call API to get encrypted params for reimbursement."""
    payload = {
        "empcode": empcode,
    }
    resp = requests.post(f"{BASE_URL_ENCRYPT}/get_encrypted_params", json=payload)
    resp.raise_for_status()
    data = resp.json()
    if data.get("errorcode") != 0:
        raise ValueError(f"Encryption failed: {data}")
    return data.get("encryptedParams")

def get_reimbursement_data(encrypted_params: str) -> Dict[str, Any]:
    """Call API to fetch reimbursement info."""
    payload = {"params": encrypted_params}
    resp = requests.post(BASE_URL_REIMBURSE, json=payload)
    data = resp.json()
    # explicit error handling
    if isinstance(data, dict) and data.get("errorCode") == 1:
        return {"error": "Failed to fetch reimbursement info"}
    return data

def send_reimbursement_1(empcode: str):
    """Fetch reimbursement info for a single month."""
    try:
        encrypted = get_reimbursement_encrypted(empcode)
        raw_data = get_reimbursement_data(encrypted)
        return  raw_data
    except Exception as e:
        return {"error": str(e)}

def reimbursement_api(empcode: str, months: List[str]) -> Dict[str, Any]:
    """Fetch reimbursement info for multiple months in parallel."""
    print("Fetching reimbursement info for:", empcode, months)
    results = {}

    # Sort months YYYY-MM before sending requests
    months = sorted(months, key=lambda x: datetime.strptime(x, "%Y-%m"))
    print("Sorted months:", months)

    with ThreadPoolExecutor(max_workers=len(months)) as executor:
        futures = []

        # Submit each month as a separate task
        for month in months:
            future = executor.submit(send_reimbursement_1, empcode, month)
            futures.append(future)

        # Collect results as they complete
        for future in as_completed(futures):
            month, data = future.result()
            results[month] = data

    print("All reimbursement results:", results)
    return results
