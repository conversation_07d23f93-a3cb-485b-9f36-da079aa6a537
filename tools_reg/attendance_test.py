import requests
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from packages import *
from tools_reg.emp_jwt_gen import *

BASE_URL=os.getenv("SSO_BASE_URL")

SSO_BASE_URL1=os.getenv("SSO_BASE_URL1")
SSO_BASE_URL2=os.getenv("SSO_BASE_URL2")
DEFAULT_TIMEOUT=int(os.getenv("DEFAULT_TIMEOUT"))

MAX_WORKERS = 5  # Reasonable limit to avoid overwhelming the server

# BASE_URL = "http://192.168.13.129:2720/ssoNodeAPI/sso"

def get_encrypted_params(empcode: str, month: str) -> str:
    """Call API to get encrypted params."""
    payload = {"empcode": empcode, "month": month}
    resp = requests.post(f"{BASE_URL}/get_encrypted_params", json=payload,timeout=DEFAULT_TIMEOUT)
    resp.raise_for_status()
    return resp.json()["encryptedParams"]

def get_attendance_data(empcode:str,encrypted_params: str) -> dict:
    """Call API to fetch attendance data."""
    payload = {"params": encrypted_params}
    token_result = jwt_token_gen(empcode)

    if not token_result["success"]:
        return {"valid": False, "data": None, "error": token_result["error"]}
    
    jwt_token = token_result["token"]

   
    # resp = requests.post(f"{BASE_URL}/getAttendanceDataEncrypted",
    #                              headers={"Authorization": f"Bearer {jwt_token}"},
    # json=payload)
    resp = requests.post(
            f"{BASE_URL}/getAttendanceDataEncrypted",
            headers={"Authorization": f"Bearer {jwt_token}"},
            json=payload,
            timeout=DEFAULT_TIMEOUT
        )
    resp.raise_for_status()
    return resp.json()

# def format_attendance(attendance_data: dict) -> dict:
#     """Format raw API response into a summary."""
#     try:
#         att = attendance_data["data"]["data"][0]["attendance"]["att_data"]
#         return {
#             "present_days": att.get("total_present_days", 0),
#             "absent_days": att.get("total_absent_days", 0),
#             "working_days": att.get("total_working_days", 0),
#             "working_hours": att.get("tot_working", "0 hrs 0 mins"),
#             "lop": att.get("lop", 0),  # Loss of Pay
#         }
#     except Exception:
#         return {"error": "No attendance data found"}
def fetch_month_attendance(empcode, month):
    """Helper function to fetch attendance for a single month."""
    try:
        print("hit_aatnda")
        encrypted = get_encrypted_params(empcode, month)
        raw_data = get_attendance_data(empcode,encrypted)
        print(raw_data)
        return month, raw_data
    except Exception as e:
        return month, {"error": str(e)}
    
def get_employee_attendance(empcode: str, months: list[str]) -> dict:
    results = {}
    with ThreadPoolExecutor(max_workers=len(months)) as executor:
        futures = [executor.submit(fetch_month_attendance, empcode, month) for month in months]
        for future in as_completed(futures):
            month, data = future.result()
            results[month] = data
    print("all reusly came)")
    return results
# def get_employee_attendance(empcode: str, months: list[str]) -> dict:
#     """
#     Main function:
#     Input: empcode, list of months (YYYY-MM)
#     Output: dict of summaries for each month
#     """
#     results = {}
#     for month in months:
#         try:
#             encrypted = get_encrypted_params(empcode, month)
#             raw_data = get_attendance_data(encrypted)
#             # summary = format_attendance(raw_data)
#             results[month] = raw_data
#         except Exception as e:
#             results[month] = {"error": str(e)}
#     return results
