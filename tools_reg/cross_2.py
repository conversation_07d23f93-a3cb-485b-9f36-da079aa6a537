
# === CONFIGURATION ===
# COLLECTION = "Hr_policies_JD_all"
   # Final context chunks after rerank
from packages import *


COLLECTION = os.getenv("QDRANT_COLLECTION")
QDRANT_URL = os.getenv("QDRANT_URL")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL")
LLM_URL = os.getenv("LLM_URL")
LLM_MODEL = os.getenv("LLM_MODEL")
VECTOR_TOP_K = int(os.getenv("VECTOR_TOP_K", 30))  # default 30 if not found
FINAL_TOP_K = int(os.getenv("FINAL_TOP_K", 10))    # default 10 if not found
CROSS_EMBEDDING_MODEL=os.getenv("CROSS_EMBEDDING_MODEL")
# === INITIALIZATION ===
# app = FastAPI(title="HR Policy Chatbot API")

# === REQUEST & RESPONSE MODELS ===
class QueryRequest(BaseModel):
    emp_id: str
    question: str

class QueryResponse(BaseModel):
    emp_id: str
    question: str
    answer: str
    context: List[str]
    sources: List[dict]



def hr_documnet_chat(question):
    qdrant_client = QdrantClient(url=QDRANT_URL)
    #print(qdrant_client)
    embedding_model = HuggingFaceEmbeddings(model_name=EMBEDDING_MODEL)
    cross_encoder = CrossEncoder(CROSS_EMBEDDING_MODEL)  # Download on first run

      # 1. Embed the question
    query_vector = embedding_model.embed_query(question)

    # 2. Query Qdrant for top relevant context chunks
    search_result = qdrant_client.search(
        collection_name=COLLECTION,
        query_vector=query_vector,
        limit=VECTOR_TOP_K,
        with_payload=True,
    )
     # 2. Package up for cross-encoder
    candidates = []
    for r in search_result:
        candidates.append({
            "text": r.payload.get("text", ""),
            "type": r.payload.get("type", ""),
            "source": r.payload.get("source", ""),
            "page": r.payload.get("page", ""),
            "vector_score": float(r.score),
        })
    # 3. Rerank with cross-encoder (slow, but super-accurate)
    pairs = [(question, c["text"]) for c in candidates]
    scores = cross_encoder.predict(pairs)
    for c, s in zip(candidates, scores):
        c["cross_score"] = float(s)
    # 4. Sort by cross-encoder score (desc), pick best FINAL_TOP_K chunks
    best_chunks = sorted(candidates, key=lambda x: -x["cross_score"])[:FINAL_TOP_K]
    # 5. Prepare context string for LLM, with citations
    context = []
    context_metadata = []
    for c in best_chunks:
        citation = f"[From \"{c['source']}\" page {c['page']}]"
        context.append(f"{citation}\n{c['text']}")
        context_metadata.append({
            "type": c["type"], "source": c["source"], "page": c["page"], "score": c["cross_score"]
        })
    context_prompt = "\n---\n".join(context)
    ##print(context_prompt)
    return context_prompt, context_metadata


def llm_call(context_str,question):
    # system_prompt = "You are an efficient and accurate HR policy assistant. Use the provided HR document context to answer queries accurately."
    system_prompt = (
    "You are an efficient and accurate HR policy assistant. "
    "Use the provided HR document context to answer the queries briefly and precisely. "
    "Do not add extra information. If the answer is not clearly in the context, respond with 'I don't know'."
)

    user_prompt = f"CONTEXT:\n{context_str}\n\nQUESTION:\n{question}\n"
    
    # 4. Call the deployed LLM
    llm_payload = {
        "model": LLM_MODEL,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]
    }

    llm_response = requests.post(LLM_URL, json=llm_payload, timeout=40)
    llm_response.raise_for_status()
    result = llm_response.json()
    answer = result["choices"][0]["message"]["content"]
    return answer
    
# === ROUTE ===
# class QueryRequest(BaseModel):
#     emp_id: str
#     question: str

# @app.post("/ask_hr")
# def ask_question(request: QueryRequest):
#     try:
#         question=request.question
#         context_str,context_metadata=hr_documnet_chat(question)
#         ##print(context_str)
#         # ##print("")
#         answer=llm_call(context_str,question)
      

      
#         return {"emp_id":request.emp_id, "question":request.question,"answer":answer,"error":0}
#         # return QueryResponse(
#         #     emp_id=request.emp_id,
#         #     question=request.question,
#         #     answer=answer,
#         #     context=context_chunks,
#         #     sources=context_metadata
#         # )
#     except Exception as e:
#         return {"emp_id":request.emp_id, "question":request.question,"answer":"","error":1,"error_msg":e}

# if __name__ == "__main__":
#     api_port=5555
#     # with Suppress##prints():
#     uvicorn.run(app, host='0.0.0.0', port=int(api_port), proxy_headers=True, forwarded_allow_ips='*') # THIS LINE)

#     #uvicorn.run("classifier:app", host='0.0.0.0', port=int(api_port), proxy_headers=True, forwarded_allow_ips='*',workers=10) # THIS LINE)
