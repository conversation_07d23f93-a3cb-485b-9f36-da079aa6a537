import requests
from datetime import datetime
from tools_reg.emp_jwt_gen import *

from packages import *

SSO_BASE_URL=os.getenv("SSO_BASE_URL")

SSO_BASE_URL1=os.getenv("SSO_BASE_URL1")

SSO_BASE_URL2=os.getenv("SSO_BASE_URL2")
DEFAULT_TIMEOUT=int(os.getenv("DEFAULT_TIMEOUT"))

def buddy_ref_api(empcode):
    print("***********************************#@@@@@@#@@@@@@")
    today = datetime.today().date()
    try:

        base_url = SSO_BASE_URL#"http://192.168.13.129:2720/ssoNodeAPI/sso"
        base_url_2 = SSO_BASE_URL1#"http://192.168.16.33:2720/ssoNodeAPI/sso"
        base_url_3 = SSO_BASE_URL2#"http://192.168.131.83/ssoNodeAPI/sso"

        # Step 1: Get encrypted params
        encrypt_api = f"{base_url}/get_encrypted_params"
        encrypt_resp = requests.post(encrypt_api, json={"empcode": empcode}).json()
        
        if encrypt_resp.get("errorcode") != 0:
            print("Encryption API failed:", encrypt_resp)
            return "error_occured..pls try again"
        
        encrypted_params = encrypt_resp.get("encryptedParams")
        
        
        token_result = jwt_token_gen(empcode)

        if not token_result["success"]:
            return {"valid": False, "data": None, "error": token_result["error"]}
        
        jwt_token = token_result["token"]
        print("jwt_genartated")
        # Step 2: Get buddy referral data
        referral_api = f"{base_url}/getBuddyReferralDataEncrypted"
        referral_resp = requests.post(referral_api,             
                                      headers={"Authorization": f"Bearer {jwt_token}"},
                                      json={"params": encrypted_params},            
                                      timeout=DEFAULT_TIMEOUT)
        referral_resp=referral_resp.json()
        
        if referral_resp.get("errorCode") != 0:
            print("Referral API failed:", referral_resp)
            return "error_occured..pls try again"
        
        data = referral_resp.get("data", [])
        if not data:
            print("No referral data found.")
            return "No referral data found."
        print(data)
        # Step 3: Filter only records where referral_empcode is not null
        valid_records = [d for d in data if d.get("referral_empcode")]
        print("&&&&&&&&&&&&&&&&&&&&&&&&&valid_records")
        print(valid_records)
        print("&&&&&&&&&&&&&&&&&&&&&&&&&valid_records")


        
        report = []
        for record in valid_records:
            referral_name = record["referral_name"]
            referral_empcode = record["referral_empcode"]
            update_time = record["updatetime"]

            # Step 4: Generate token
            token_api = f"{base_url_2}/generate_token"
            token_resp = requests.post(token_api, json={"user": {"searchKeyword": referral_empcode}}).json()

            if token_resp.get("errorcode") != 0:
                print(f"Token API failed for {referral_empcode}: {token_resp}")
                continue

            jwt_token = token_resp.get("jwt_token")

            # Step 5: Get employee details
            emp_detail_api = f"{base_url_3}/get_employee_details"
            headers = {"Authorization": f"Bearer {jwt_token}"}
            emp_resp = requests.post(emp_detail_api, headers=headers).json()

            if emp_resp.get("errorcode") != 0:
                report.append({
                "referral_name": referral_name,
                "referral_empcode": referral_empcode,
                # "update_time": update_time,
               
                "status": "left the organization. Referral bonus not eligible."
                })
                print(f"Employee details API failed for {referral_empcode}: {emp_resp}")
                continue
        

            emp_data = emp_resp["data"][0]
            doj = emp_data.get("date_of_joining")
            dol = emp_data.get("date_of_leaving")

            doj_date = datetime.strptime(doj, "%Y-%m-%d").date() if doj else None
            dol_date = datetime.strptime(dol, "%Y-%m-%d") if dol else None

            doj_diff = (today - doj_date).days if doj_date else None
            dol_diff = (today - dol_date.date()).days if dol_date else None


            from dateutil.relativedelta import relativedelta

            doj_date = datetime.strptime(doj, "%Y-%m-%d").date()
            months_diff = (today.year - doj_date.year) * 12 + (today.month - doj_date.month)
            eligibility_date = doj_date + relativedelta(months=6)
            delta_days = (eligibility_date - today).days
            
            
            messages=[]
            if delta_days <= 0:
                if dol:
                    messages.append(f"{referral_name} joined on {doj} and completed 6 months but has left the organization {dol}. Referral bonus not eligible.")
                else:
                    messages.append(f"{referral_name} joined on {doj} and has completed 6 months. You are eligible for the referral bonus. Connect with HR!")
            else:
                months_remaining = delta_days // 30
                days_remaining = delta_days % 30
                messages.append(
                    f"{referral_name} joined on {doj}. Referral bonus eligibility in "
                    f"{months_remaining} month(s) and {days_remaining} day(s), after {eligibility_date}."
                )
                
            status="\n".join(messages)

            # Step 6: Check 60-day validity
            # is_valid = False
            # if dol is None and doj_diff is not None and doj_diff >= 60:
            #     is_valid = True
            # Step 6: Check 60-day validity
            # status = "Not Valid Yet"
            # if doj_diff is not None and doj_diff >= 60:
            #     if dol is None:  
            #         status = "Valid Referral Buddy"
            #     else:  
            #         status = "Not Valid Referral Buddy as candidate Left Organization"


            report.append({
                "referral_name": referral_name,
                "referral_empcode": referral_empcode,
                # "update_time": update_time,
                "date_of_joining": doj,
                "date_of_leaving": dol,
                "days_since_joining": doj_diff,
                "days_since_leaving": dol_diff,
                "status": status
            })
        
        
        valid_records=report
        print(valid_records)
        return valid_records
    
    except Exception as e:
        print("***************************")
        print(e)
        print("***************************")

        
        return "error_occured..pls try again"
