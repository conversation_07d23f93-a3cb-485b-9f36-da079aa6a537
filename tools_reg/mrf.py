import requests


from packages import *
from tools_reg.emp_jwt_gen import *
DEFAULT_TIMEOUT=int(os.getenv("DEFAULT_TIMEOUT"))

SSO_BASE_URL = os.getenv("SSO_BASE_URL")

def mrf_api(empcode):
    
    import requests
    try:

        
        GET_ENCRYPTED_PARAMS_URL = f"{SSO_BASE_URL}/get_encrypted_params"
        GET_MRF_DATA_URL = f"{SSO_BASE_URL}/getMrfDetailsEncrypted"

        # from_date = "2025-01-01"
        # to_date = "2025-12-31"
        # from datetime import datetime

        # Get current date
        current_date = datetime.now()
        current_year = current_date.year

        # Set from_date to January 1st of current year
        from_date = f"{current_year}-01-01"

        # Set to_date to December 31st of current year  
        to_date = f"{current_year}-12-31"

        print(f"From: {from_date}")
        print(f"To: {to_date}")
        # Step 1: Get encryptedParams
        payload = {
            "empcode": empcode,
            # "from_date": from_date,
            # "to_date": to_date
        }

        headers = {"Content-Type": "application/json"}

        # try:
            ##print("Fetching encrypted params...")
        res1 = requests.post(GET_ENCRYPTED_PARAMS_URL, json=payload, headers=headers)
        res1.raise_for_status()
        res1_json = res1.json()

        if res1_json.get("errorcode") != 0 or "encryptedParams" not in res1_json:
            # return {"valid": False, "data": None, "error": "Failed to get encrypted params"}
            ##print("Failed to get encrypted params:", res1_json)
            # return "not found"
            return "I'm experiencing technical error. Please try again. or contact HR if urgent"


            ##print("Failed to get encrypted params:", res1_json)
            # exit(1)

        encrypted_params = res1_json["encryptedParams"]
        
        token_result = jwt_token_gen(empcode)

        if not token_result["success"]:
            return "I'm experiencing technical error. Please try again. or contact HR if urgent"
        print("yesssssssssssssssss")
            # return {"valid": False, "data": None, "error": token_result["error"]}
        
        jwt_token = token_result["token"]


        ##print("Encrypted params received.")

        # Step 2: Get leave-related data using encryptedParams
        ##print("Fetching leave data...")
        res2 = requests.post(GET_MRF_DATA_URL, 
                            json={"params": encrypted_params}, 
                            headers={"Authorization": f"Bearer {jwt_token}"},
                            timeout=DEFAULT_TIMEOUT

                                            )
        # except Exception as ex:
        #     return "ERROR WHILE REtreiving"
            # pl_summary_input="ERROR WHILE REtreiving"
        res2.raise_for_status()
        res2_json = res2.json()
        print(res2_json)

        if res2_json.get("errorCode") == 0 and res2_json["data"] !=[]:  # Changed from "error.code" to "errorCode"
            pl_data = res2_json["data"]#.get("PL")
            if pl_data:
                ##print("\n--- PL Leave Data ---")
                # for k, v in pl_data.items():
                    ##print(f"{k}: {v}")
                    
                context = f"""
MRF(Manpower Requirement Form) API RESPONSE STRUCTURE:

RESPONSE STATUS:
- errorCode: 0 = success, non-zero = error
- message: Status message (e.g., "Payout details retrieved successfully")
- cnt: Total count of records

COMPLETE PAYMENT HISTORY (data.data[]):
- API returns COMPLETE employee payment history across all time periods
- Each record contains:
  * id: Unique payout record ID
  * empcode: Employee ID
  * empname: Employee full name
  * payout_type: Type of payout ("Reimbursement", "Deduction", "Bonus", "Allowance", etc.)
  * payout_amount: Amount in currency (string format)
  * credit_dt: Credit/transaction date (DD-MM-YYYY format)
  * credit_detail: Description of the payout/transaction
  * display_flag: 1 = visible to employee, 0 = hidden
  * updated_by: Employee code of person who made the entry
  * updated_dt: Last updated timestamp (YYYY-MM-DD HH:MM:SS)

PAYOUT CATEGORIES:
- Different payout types exist based on credit_detail field
- LLM should interpret payout purpose from the credit_detail description provided in response

RESPONSE STRATEGY:
- API provides COMPLETE payment history for the employee
- Bot should analyze user's query and filter/respond with ONLY relevant information
- Examples:
  * "Show recent payouts" → Filter by latest dates
  * "Show bonuses" → Filter by bonus-related credit_details
  * "What did I get in 2024" → Filter by 2024 dates
  * "How much total reimbursement" → Sum all reimbursement amounts
- Do NOT overwhelm user with entire history unless specifically requested

KEY POINTS:
- payout_amount is in string format (may need conversion for calculations)
- credit_dt uses DD-MM-YYYY format
- Records contain historical data across multiple years
- Filter and summarize based on user's specific question
- Focus on what user actually needs from the complete dataset

Payout RAW API Response:
"""    
                # pl_summary_input = "\n".join([f"{k}: {v}" for k, v in pl_data.items()])
               

                import json
                payout_summary_input = context + json.dumps(pl_data)                
                print("*******************************")
                print(payout_summary_input)
                print("*******************************")
                return payout_summary_input


            else:
                pl_summary_input="not found"
                return "not found"

                ##print("PL data not found in response.")
        else:
            pl_summary_input="not found nomrf from this empid"
            return "No MRF Raised from this empid"

            ##print("Failed to get leave data:", res2_json.get("error", {}).get("msg"))

    except requests.RequestException as e:
        print("HTTP Request failed:", str(e))
        return "I'm experiencing technical error. Please try again. or contact HR if urgent2"
    except Exception as ex:
        print("Unexpected error:", str(ex))
        return "I'm experiencing technical error. Please try again. or contact HR if urgent3"
