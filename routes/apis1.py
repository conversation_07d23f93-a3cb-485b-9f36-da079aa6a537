
from packages import *
from  hr_bot import *

TOKEN_EXPIRATION_HOURS = 24
# Encryption configuration
SECRET_KEY = os.getenv("JWT_SECRET_KEY")#, "your-super-secret-jwt-key-change-this")
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY") #or Fernet.generate_key()
cipher = Fernet(ENCRYPTION_KEY)


def encrypt_data(data: str) -> str:
    """Encrypt sensitive data."""
    return cipher.encrypt(data.encode()).decode()

def decrypt_data(encrypted_data: str) -> str:
    """Decrypt sensitive data."""
    try:
        return cipher.decrypt(encrypted_data.encode()).decode()
    except Exception as e:
        raise ValueError(f"Decryption failed: {str(e)}")

def generate_employee_token(emp_id: str) -> str:
    """Generate JWT token with encrypted emp_id."""
    encrypted_emp_id = encrypt_data(emp_id)
    payload = {
        "encrypted_emp_id": encrypted_emp_id,
        "iat": datetime.utcnow(),
        "exp": datetime.utcnow() + timedelta(hours=TOKEN_EXPIRATION_HOURS),
        "type": "employee_token"
    }
    return jwt.encode(payload, SECRET_KEY, algorithm="HS256")


def verify_and_decrypt_token(token: str) -> dict:
    """Verify JWT token and decrypt emp_id."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        encrypted_emp_id = payload.get("encrypted_emp_id")
        if not encrypted_emp_id:
            raise ValueError("Invalid token structure")
        
        emp_id = decrypt_data(encrypted_emp_id)
        return {"valid": True, "emp_id": emp_id, "payload": payload}
    except jwt.ExpiredSignatureError:
        return {"valid": False, "error": "Token expired"}
    except jwt.InvalidTokenError:
        return {"valid": False, "error": "Invalid token"}
    except Exception as e:
        return {"valid": False, "error": str(e)}
    
    

HISTORY_MAP = int(os.getenv("HISTORY_MAP", 10))  # default 10
SEND_OTP=os.getenv("SEND_OTP")

class Message(BaseModel):
    role: str
    content: str

class QueryRequest(BaseModel):
    # emp_id: str
    question: str
    session_id:Optional[str] = None  # New field for session management

    
    history: List[Message] = []


def get_client_ip(request: Request) -> str:
    """Extract client IP address from request."""
    x_forwarded_for = request.headers.get('x-forwarded-for')
    if x_forwarded_for:
        return x_forwarded_for.split(',')[0].strip()
    return request.client.host if request.client else "unknown"


@fastapi_app.post("/ask_hr")
def ask_question(request: QueryRequest,http_request: Request,   
    authorization: str = Header(..., description="Bearer JWT token")
):
    start_time = time.time()
    user_ip = get_client_ip(http_request)
    
     # Check if Authorization header exists and starts with "Bearer "
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header format")
    
    # Extract JWT token (remove "Bearer " prefix)
    encrypted_token = authorization.split(" ")[1]

    token_result = verify_and_decrypt_token(encrypted_token)
    print(token_result)
    print("token_result)))))))))))",token_result)
    if not token_result["valid"]:
        return {
            "error": 1,
            "error_msg": token_result["error"],
            # "sessions": [],
            # "total_sessions": 0
        }



    # global EMP_ID
    emp_id = token_result["emp_id"].strip() if token_result["emp_id"] else ""
    encrypted_emp_id=token_result["payload"]["encrypted_emp_id"].strip() if token_result["payload"]["encrypted_emp_id"] else encrypt_data(emp_id)
    # emp_id = request.emp_id.strip() if request.emp_id else ""
    question = request.question.strip()
    session_id = request.session_id
    history = [{"role": h.role, "content": h.content} for h in request.history]

    def build_response(emp_id, session_id, question, answer, error=0, error_msg=None):
        response_time = round(time.time() - start_time, 2) if start_time else 0.0

        return {
            "error": error,
            "emp_id": encrypted_emp_id,
            "session_id": session_id,
            "question": question,
            "answer": answer,
            
            "error_msg": error_msg,
            "response_time_seconds": response_time
        }
    # Generate session_id if not provided
    if not session_id:
        session_id = str(uuid.uuid4())



    if not emp_id:
        answer = "Hi there! I'd be happy to help you with your HR questions. Could you please provide your employee ID?"
        log_chat_interaction(
            session_id=session_id,
            emp_code="",
            heading="Validation Error",
            user_question=question,
            bot_response=answer,
            response_time=time.time()-start_time,
            status_code=400,
            user_ip=user_ip,
            error_code=1,
            error_msg="Employee ID is required"
        )
        error_response=build_response(emp_id=None, session_id=session_id, question=question, answer=answer, error=1, error_msg="Employee ID is required")
        return error_response
      


    validation = validate_employee(emp_id)
    if not validation["valid"]:
        answer = "I'm having trouble finding your employee record. Please double-check your employee ID or contact IT support."
        log_chat_interaction(
            session_id=session_id,
            emp_code=emp_id,
            heading="Authentication Error", 
            user_question=question,
            bot_response=answer,
            response_time=time.time()-start_time,
            status_code=401,
            user_ip=user_ip,
            error_code=1,
            error_msg=f"Employee validation failed: {validation.get('error')}"
        )
        
        error_response=build_response(emp_id=encrypted_emp_id, session_id=session_id, question=question, answer=answer, error=1, error_msg=f"Employee validation failed: {validation.get('error')}")

        return error_response


     # --- Step 2: Set global EMP_ID ---
    set_emp_id(emp_id)
    print(f"Global EMP_ID set to: {config.EMP_ID}")

    try:
        # --- Step 3: Session Handling ---

        
        session_check = check_session_exists(session_id)
        # If session doesn't exist, create new session with heading
        if not session_check["exists"]:
            heading = generate_chat_heading(question, validation.get("data", {}))
            create_new_session(session_id, emp_id, heading, user_ip)
        else:
            heading = session_check["session_data"]["heading"]
            
        # --- Step 4: Prepare message history ---
        messages = []
        for msg in history[-HISTORY_MAP:]:  # Keep last 10 messages for context
            if msg["role"] == "user":
                messages.append(HumanMessage(content=msg["content"]))
            elif msg["role"] == "assistant":
                messages.append(AIMessage(content=msg["content"]))

        # Add the new user question
        current_question = f"{question} (empcode: {emp_id})"
        messages.append(HumanMessage(content=current_question))

        # Create initial state with employee context
        initial_state = {
            "messages": messages,
            "employee_context": validation.get("data", {}),
            "payslip_state": {}
        }

        # --- Step 5: Stream response ---
        answer = ""
        for event in app.stream(initial_state, stream_mode="values"):
            last_message = event["messages"][-1]
            if hasattr(last_message, "content"):
                answer = last_message.content
                # Clean up any thinking tags
                if "</think>" in answer:
                    answer = answer.split("</think>")[-1].strip()

        # Get employee name for response context
        emp_name = validation["data"].get("empname") if validation["data"] else None
        
        if emp_name and answer:
            # Keep the answer as is, just ensure it's clean
            answer = answer.strip()
        else:
            answer = "I apologize, but I'm experiencing technical difficulties. Please try again later or contact HR if urgent."
            
            
          # Log successful interaction
        log_chat_interaction(
            session_id=session_id,
            emp_code=emp_id,
            heading=heading,
            user_question=question,
            bot_response=answer,
            response_time=time.time()-start_time,
            status_code=200,
            user_ip=user_ip,
            error_code=0

        )
        suc_response=build_response(emp_id=encrypted_emp_id, session_id=session_id, question=question, answer=answer)
        return suc_response



    except Exception as e:
        
        logger.error(f"Error processing request: {str(e)}")
        answer = "I apologize, but I'm experiencing technical difficulties. Please try again later or contact HR if urgent."
        log_chat_interaction(
            session_id=session_id,
            emp_code=emp_id,
            heading="System Error",
            user_question=question,
            bot_response=answer,
            response_time=time.time()-start_time,
            status_code=500,
            user_ip=user_ip,
            error_code=1,
            error_msg=str(e)
        )
        err_response=build_response(emp_id=encrypted_emp_id, session_id=session_id, question=question, answer=answer, error=1, error_msg=str(e))
        
        return err_response

    
    
# === Health check endpoint ===
@fastapi_app.get("/health")
def health_check():
    """Health check endpoint to verify API and database connectivity."""
    return {
        "status": "healthy",
        "database_connected":db is not None,
        "timestamp": datetime.now().isoformat()
    }

# @fastapi_app.get("/sessions/employee/{encrypted_token}")
@fastapi_app.get("/sessions")

# def get_employee_sessions(encrypted_token: str, limit: int = 20):
def get_employee_sessions(
    authorization: str = Header(..., description="Encrypted employee token"),
    limit: int = 20
):
    """Get recent sessions for an employee."""
    # encrypted_token=key
    try:
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization header format")
    
    # Extract JWT token (remove "Bearer " prefix)
        encrypted_token = authorization.split(" ")[1]
        token_result = verify_and_decrypt_token(encrypted_token)
        print(token_result)
        print("token_result)))))))))))",token_result)
        if not token_result["valid"]:
            return {
                "error": 1,
                "error_msg": token_result["error"],
                "sessions": [],
                "total_sessions": 0
            }
        
        # emp_code = token_result["emp_id"]
        emp_code = token_result["emp_id"].strip() if token_result["emp_id"] else ""

        encrypted_emp_id=token_result["payload"]["encrypted_emp_id"].strip() if token_result["payload"]["encrypted_emp_id"] else encrypt_data(emp_id)

        if chat_sessions_collection is None:
            return {
                        "error": 1,
                        "emp_code": encrypted_emp_id,
                        "error_msg": "Database not available",
                        "sessions": [],
                        "total_sessions": 0
                    }    
        sessions = list(chat_sessions_collection.find(
            {"emp_code": emp_code}
        ).sort("last_activity", -1))#.limit(limit))
        
        # Convert ObjectId to string for JSON serialization
        for session in sessions:
            session["_id"] = str(session["_id"])
        
        return {
                    "error": 0,
                    "emp_code": encrypted_emp_id,
                    "sessions": sessions,
                    "total_sessions": len(sessions)
                }    
    except Exception as e:
        logger.error(f"Error fetching employee sessions: {e}")
        return {
            "error": 1,
            "emp_code":encrypted_emp_id,
            "error_msg": str(e),
            "sessions": [],
            "total_sessions": 0
        }
# === Session management endpoints ===
@fastapi_app.get("/session/{session_id}")
def get_session_info(session_id: str):
    """Get session information."""
    try:
        session_check = check_session_exists(session_id)
        if session_check["exists"]:
            session_data = session_check["session_data"]
            return {
                "error":0,
                "session_id": session_id,
                "exists": True,
                "emp_code": encrypt_data(session_data.get("emp_code")),
                "heading": session_data.get("heading"),
                "created_at": session_data.get("created_at"),
                "last_activity": session_data.get("last_activity")
            }
        else:
            return {
                "error":0,

                "session_id": session_id,
                "exists": False
            }
    except Exception as e:
        return {
                "error":1,
                "error_msg":str(e),

                "session_id": session_id,
                "exists": False
            }
        

@fastapi_app.get("/chat-logs/session/{session_id}")
def get_session_chat_logs(session_id: str, limit: int = 50):
    """Get chat logs for a specific session formatted for frontend display."""
    if chat_logs_collection is None:
        return {"error":1,            
                "session_id": session_id,
                "error_msg": "Database not available", 
                "data": [],
                "total_messages":None,
                "total_interactions":None
                
                }
    try:
        # Fetch logs from database
        logs = list(chat_logs_collection.find(
            {"session_id": session_id}
        ).sort("timestamp", 1).limit(limit))
        
        # Transform logs into chat message format
        chat_messages = []
        for log in logs:
            # Add user message user assistant
            chat_messages.append({
                "role": "user",
                "message": log["user_question"],
                "timestamp": log["timestamp"].isoformat() if isinstance(log["timestamp"], datetime) else log["timestamp"]
            })
            
            # Add assistant message
            chat_messages.append({
                "role": "assistant", 
                "message": log["bot_response"],
                "timestamp": log["timestamp"].isoformat() if isinstance(log["timestamp"], datetime) else log["timestamp"]
            })
        
        return {
            "error":0,
            "session_id": session_id,
            "data": chat_messages,
            "total_messages": len(chat_messages),
            "total_interactions": len(logs)
        }
        
    except Exception as e:
        logger.error(f"Error fetching session chat logs: {e}")
        return {
            "error": 1,
            "session_id": session_id,
            "error_msg": str(e),
            "data": [],
            "total_messages": None,
            "total_interactions": None
        }

@fastapi_app.get("/chat-logs/session/{session_id}/raw")
def get_session_chat_logs(session_id: str): #limit: int = 50):
    """Get chat logs for a specific session."""
    if chat_logs_collection is None:
        return {
                    "error": 1,
                    "session_id": session_id,
                    "error_msg": "Database not available",
                    "logs": []
                }    
    try:
        logs = list(chat_logs_collection.find(
            {"session_id": session_id}
        ).sort("timestamp", 1))#.limit(limit))
        
        # Convert ObjectId to string for JSON serialization
        for log in logs:
            log["_id"] = str(log["_id"])
        
        return {
                    "error": 0,
                    "session_id": session_id,
                    "logs": logs
                } 
    except Exception as e:
        logger.error(f"Error fetching session chat logs: {e}")
        return {
                    "error": 1,
                    "session_id": session_id,
                    "error_msg": str(e),
                    "logs": []
                }

@fastapi_app.get("/analytics/daily-stats")
def get_daily_stats(date_str: str = None):
    """Get daily analytics stats."""
    if chat_logs_collection is None:
        return {
                    "error": 1,
                    "error_msg": "Database not available",
                    "date": date_str,
                    "stats": {}
                }    
    try:
        if date_str:
            target_date = datetime.strptime(date_str, "%Y-%m-%d")
        else:
            target_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        next_date = target_date + timedelta(days=1)
        
        pipeline = [
            {
                "$match": {
                    "timestamp": {
                        "$gte": target_date,
                        "$lt": next_date
                    }
                }
            },
            {
                "$group": {
                    "_id": None,
                    "total_interactions": {"$sum": 1},
                    "unique_employees": {"$addToSet": "$emp_code"},
                    "unique_sessions": {"$addToSet": "$session_id"},
                    "avg_response_time": {"$avg": "$response_time_seconds"},
                    "max_response_time": {"$max": "$response_time_seconds"},
                    "min_response_time": {"$min": "$response_time_seconds"},
                    "successful_interactions": {
                        "$sum": {"$cond": [{"$eq": ["$status_code", 200]}, 1, 0]}
                    },
                    "error_interactions": {
                        "$sum": {"$cond": [{"$ne": ["$status_code", 200]}, 1, 0]}
                    }
                }
            }
        ]
        
        result = list(chat_logs_collection.aggregate(pipeline))
        
        if result:
            stats = result[0]
            stats["unique_employees_count"] = len(stats["unique_employees"])
            stats["unique_sessions_count"] = len(stats["unique_sessions"])
            del stats["unique_employees"]
            del stats["unique_sessions"]
            del stats["_id"]
        else:
            stats = {
                "total_interactions": 0,
                "unique_employees_count": 0,
                "unique_sessions_count": 0,
                "avg_response_time": 0,
                "successful_interactions": 0,
                "error_interactions": 0
            }
        
        return {
            "error": 0,
            "date": target_date.strftime("%Y-%m-%d"),
            "stats": stats
        }
    except Exception as e:
        logger.error(f"Error fetching daily stats: {e}")
        return {
                    "error": 1,
                    "error_msg": str(e),
                    "date": date_str,
                    "stats": {}
                }
    
class OTPSend(BaseModel):

    emp_id: str
    
    
@fastapi_app.post("/employee/send-verification")
def send_verification(request: OTPSend):
    """Validate employee and send verification code to mobile number."""
    start_time = time.time()
    emp_id=request.emp_id
    # session_id = f"session_{int(time.time())}"

    try:
        # Step 1: Validate employee
        validation = validate_employee(emp_id)
        if not validation["valid"]:
        

            return {
                "error": 1,
                "emp_id": emp_id,
                "error_msg": f"Employee validation failed: {validation.get('error')}",
            }

        # Step 3: Extract mobile number
        emp_data = validation.get("data", {})
        mobile_num = emp_data.get("mobile_num")

        if not mobile_num:
            return {
                "error": 1,
                "emp_id": emp_id,
                # "session_id": session_id,
                "error_msg": "Mobile number not found for this employee.",
                # "message": "No mobile number available for verification.",
                # "verification_response": None
            }

        # Step 4: Call external API
        api_url = SEND_OTP
        params = {
            "mobile": mobile_num,
            "wap": 35,
            "resend": 0,
            "source": "jdchat"
        }

        try:
            resp = requests.get(api_url, params=params, timeout=10)
            resp.raise_for_status()
            api_result = resp.json() if resp.headers.get("content-type") == "application/json" else {"raw_response": resp.text}
        except Exception as e:
            return {
                "error": 1,
                "emp_id": emp_id,
                "error_msg": f"Failed to call verification API: {str(e)}",
                "otp":False
            }

        # Step 5: Success response
        return {
            "error": 0,
            "emp_id": emp_id,
            "mobile": mobile_num,
            "result":api_result["result"],
            "verification_response": api_result,
            "otp":True
        }

    except Exception as e:
        logger.error(f"Error in send_verification endpoint: {e}")
        return {
            "error": 1,
            "emp_id": emp_id,
            "error_msg": str(e),
        }

class OTPRequest(BaseModel):
    otp: str
    contact_number: str
    emp_id: str
    
    
@fastapi_app.post("/employee/verify-otp")
def verify_otp( request: OTPRequest):
    """Verify OTP for an employee using the external API."""
    emp_code=request.emp_id

    try:
        # Step 1: Build API URL
        api_url = SEND_OTP
        params = {
            "mobile": request.contact_number,
            "wap": 35,
            "resend": 0,
            "vcode": request.otp
        }

        # Step 2: Call external API
        try:
            resp = requests.get(api_url, params=params, timeout=10)
            resp.raise_for_status()
            api_result = resp.json()
        except Exception as e:
            return {
                "error": 1,
                "emp_code": emp_code,
                "error_msg": f"Failed to call OTP validation API: {str(e)}",
                
            }

        # Step 3: Parse success / error
        if api_result.get("result") == "SUCCESS":
             # Generate encrypted token for future requests
            encrypted_token = generate_employee_token(emp_code)
            
            # Encrypt the response data
            encrypted_emp_id = encrypt_data(emp_code)
            return {
                "error": 0,
                "emp_code": encrypted_emp_id,#emp_code,
                "encrypted_token": encrypted_token,
                "validation": True,
                "validation_result": api_result,
                "message": "OTP validation successful",
            }
        else:
            return {
                "error": 0,
                "emp_code": encrypted_emp_id,
                "validation": False,
                "validation_result": api_result,
                "message":  "OTP validation failed"
                }

    except Exception as e:
        logger.error(f"Error in verify_otp endpoint: {e}")
        return {
            "error": 1,
            "emp_code": encrypted_emp_id,
            "error_msg": str(e)
        }




@fastapi_app.get("/decrypt_emp")
def decrypt_emp_token(emp_token: str = Header(..., description="Encrypted employee token")):
    """Endpoint to decrypt employee token and return emp_id."""
    try:
        # 🔑 Decrypt employee token
        emp_id = decrypt_data(emp_token)
        return {"error":0,"valid": True,"emp_id": emp_id}
    except Exception as e:
        return {"error":1,"valid": False, "error": str(e)}

        # raise HTTPException(status_code=400, detail=f"Invalid token: {str(e)}")

