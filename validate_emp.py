from packages import *
from tools_reg.emp_jwt_gen import *
SSO_BASE_URL1=os.getenv("SSO_BASE_URL1")
SSO_BASE_URL2=os.getenv("SSO_BASE_URL2")

def validate_employee(emp_id: str) -> dict:
    try:
        token_result = jwt_token_gen(emp_id)
    
        if not token_result["success"]:
            return {"valid": False, "data": None, "error": token_result["error"]}
        
        jwt_token = token_result["token"]

        emp_resp = requests.post(
            f"{SSO_BASE_URL2}/get_employee_details",
            headers={"Authorization": f"Bearer {jwt_token}"},
            timeout=10
        )
        emp_resp.raise_for_status()
        emp_json = emp_resp.json()

        if emp_json.get("exists") == 1 and emp_json.get("data"):
            return {"valid": True, "data": emp_json["data"][0], "error": None}
        else:
            return {"valid": False, "data": None, "error": emp_json.get("msg", "Employee not found")}

    except requests.RequestException as e:
        return {"valid": False, "data": None, "error": f"Request error: {str(e)}"}
    except Exception as e:
        return {"valid": False, "data": None, "error": f"Unexpected error: {str(e)}"}
