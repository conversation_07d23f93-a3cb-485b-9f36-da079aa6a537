"""
Enhanced User Experience Framework for HR Bot
Provides intelligent interaction patterns, role-based access, and conversation flows
"""

from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import json
import re

class UserRole(Enum):
    """User roles with different access levels"""
    EMPLOYEE = "employee"
    TEAM_LEAD = "team_lead"
    MANAGER = "manager"
    HR_REPRESENTATIVE = "hr_rep"
    HR_ADMIN = "hr_admin"
    SYSTEM_ADMIN = "system_admin"

class InteractionType(Enum):
    """Types of user interactions"""
    INFORMATION_REQUEST = "info_request"
    DOCUMENT_REQUEST = "doc_request"
    PROCESS_INITIATION = "process_init"
    COMPLAINT_GRIEVANCE = "complaint"
    POLICY_CLARIFICATION = "policy_clarification"
    EMERGENCY_REQUEST = "emergency"

class ConversationState(Enum):
    """States of conversation flow"""
    GREETING = "greeting"
    UNDERSTANDING = "understanding"
    VALIDATION = "validation"
    PROCESSING = "processing"
    CONFIRMATION = "confirmation"
    COMPLETION = "completion"
    ESCALATION = "escalation"

@dataclass
class UserContext:
    """Comprehensive user context for personalized experience"""
    employee_id: str
    role: UserRole
    department: str
    location: str
    manager_id: Optional[str]
    access_level: int
    preferences: Dict[str, Any]
    interaction_history: List[Dict[str, Any]]
    current_session: Dict[str, Any]

@dataclass
class ConversationFlow:
    """Defines the flow of conversation for different scenarios"""
    scenario_type: str
    current_state: ConversationState
    required_steps: List[str]
    completed_steps: List[str]
    next_actions: List[str]
    validation_points: List[str]

class HRUserExperience:
    """Enhanced user experience management system"""
    
    def __init__(self):
        self.role_permissions = self._define_role_permissions()
        self.conversation_flows = self._define_conversation_flows()
        self.personalization_rules = self._define_personalization_rules()
        self.interaction_patterns = self._define_interaction_patterns()
    
    def _define_role_permissions(self) -> Dict[UserRole, Dict[str, Any]]:
        """Define what each role can access and do"""
        return {
            UserRole.EMPLOYEE: {
                "can_access": ["own_data", "general_policies", "self_service"],
                "can_request": ["payslip", "form16", "leave_balance", "attendance"],
                "can_initiate": ["leave_application", "reimbursement_claim"],
                "escalation_level": 1,
                "data_scope": "self_only"
            },
            UserRole.TEAM_LEAD: {
                "can_access": ["own_data", "team_data", "policies", "reports"],
                "can_request": ["team_reports", "approval_workflows"],
                "can_initiate": ["team_leave_approval", "performance_reviews"],
                "escalation_level": 2,
                "data_scope": "team_and_self"
            },
            UserRole.MANAGER: {
                "can_access": ["department_data", "policies", "analytics"],
                "can_request": ["department_reports", "budget_info"],
                "can_initiate": ["policy_changes", "team_restructuring"],
                "escalation_level": 3,
                "data_scope": "department_wide"
            },
            UserRole.HR_REPRESENTATIVE: {
                "can_access": ["all_employee_data", "policies", "systems"],
                "can_request": ["any_document", "system_reports"],
                "can_initiate": ["policy_updates", "system_changes"],
                "escalation_level": 4,
                "data_scope": "organization_wide"
            }
        }
    
    def _define_conversation_flows(self) -> Dict[str, ConversationFlow]:
        """Define conversation flows for different scenarios"""
        return {
            "document_request": ConversationFlow(
                scenario_type="document_request",
                current_state=ConversationState.GREETING,
                required_steps=[
                    "identify_document_type",
                    "validate_parameters",
                    "confirm_delivery_method",
                    "process_request",
                    "confirm_completion"
                ],
                completed_steps=[],
                next_actions=["ask_document_type"],
                validation_points=["date_validation", "email_validation"]
            ),
            "leave_management": ConversationFlow(
                scenario_type="leave_management",
                current_state=ConversationState.GREETING,
                required_steps=[
                    "understand_leave_need",
                    "check_leave_balance",
                    "validate_dates",
                    "guide_application_process",
                    "confirm_submission"
                ],
                completed_steps=[],
                next_actions=["clarify_leave_type"],
                validation_points=["balance_check", "date_validation", "policy_compliance"]
            ),
            "policy_inquiry": ConversationFlow(
                scenario_type="policy_inquiry",
                current_state=ConversationState.GREETING,
                required_steps=[
                    "understand_policy_area",
                    "retrieve_relevant_policies",
                    "provide_explanation",
                    "offer_additional_help"
                ],
                completed_steps=[],
                next_actions=["clarify_policy_area"],
                validation_points=["access_level_check"]
            )
        }
    
    def _define_personalization_rules(self) -> Dict[str, Any]:
        """Define rules for personalizing user experience"""
        return {
            "greeting_style": {
                "formal": "Good {time_of_day}, {title} {last_name}",
                "friendly": "Hi {first_name}!",
                "professional": "Hello {first_name}, I'm JIA, your HR assistant"
            },
            "communication_tone": {
                "new_employee": "welcoming_and_detailed",
                "experienced_employee": "efficient_and_direct",
                "manager": "professional_and_comprehensive",
                "hr_staff": "collaborative_and_technical"
            },
            "response_detail_level": {
                "high": "comprehensive_with_examples",
                "medium": "clear_and_complete",
                "low": "concise_and_direct"
            }
        }
    
    def _define_interaction_patterns(self) -> Dict[str, Any]:
        """Define patterns for different types of interactions"""
        return {
            "first_time_user": {
                "greeting": "Welcome to JIA! I'm here to help with all your HR needs.",
                "introduction": "I can assist with leave requests, payslips, policies, and much more.",
                "guidance": "Feel free to ask me anything in your preferred language.",
                "examples": ["Check my leave balance", "Send me last month's payslip", "What's the work from home policy?"]
            },
            "returning_user": {
                "greeting": "Welcome back, {name}!",
                "context_awareness": "I remember you were asking about {last_topic}.",
                "quick_actions": "Would you like to continue or start something new?",
                "shortcuts": "You can also say things like 'my usual' for frequent requests."
            },
            "power_user": {
                "greeting": "Hi {name}, what can I help you with today?",
                "efficiency_mode": "I'll keep responses concise unless you need details.",
                "advanced_options": "You can use shortcuts or ask for bulk operations.",
                "system_status": "All systems are operational and ready for your requests."
            }
        }
    
    def determine_user_role(self, employee_data: Dict[str, Any]) -> UserRole:
        """Determine user role based on employee data"""
        designation = employee_data.get('designation', '').lower()
        department = employee_data.get('department', '').lower()
        
        # Role determination logic
        if 'hr' in department:
            if any(title in designation for title in ['manager', 'head', 'director']):
                return UserRole.HR_ADMIN
            else:
                return UserRole.HR_REPRESENTATIVE
        elif any(title in designation for title in ['manager', 'head', 'director', 'vp']):
            return UserRole.MANAGER
        elif any(title in designation for title in ['lead', 'senior', 'principal']):
            return UserRole.TEAM_LEAD
        else:
            return UserRole.EMPLOYEE
    
    def personalize_greeting(self, user_context: UserContext) -> str:
        """Generate personalized greeting based on user context"""
        time_of_day = self._get_time_of_day()
        
        # Determine greeting style based on user preferences and role
        if user_context.role in [UserRole.HR_ADMIN, UserRole.MANAGER]:
            style = "professional"
        elif len(user_context.interaction_history) == 0:
            style = "friendly"
        else:
            style = "professional"
        
        # Get employee name
        emp_data = user_context.current_session.get('employee_data', {})
        first_name = emp_data.get('empname', '').split()[0] if emp_data.get('empname') else 'there'
        
        greetings = {
            "professional": f"<p>Good {time_of_day}, {first_name}! I'm JIA, your dedicated HR assistant.</p>",
            "friendly": f"<p>Hi {first_name}! 👋 Ready to help with your HR needs today.</p>",
            "formal": f"<p>Good {time_of_day}. I'm JIA, here to assist you with HR matters.</p>"
        }
        
        return greetings.get(style, greetings["professional"])
    
    def _get_time_of_day(self) -> str:
        """Determine time of day for greetings"""
        hour = datetime.now().hour
        if 5 <= hour < 12:
            return "morning"
        elif 12 <= hour < 17:
            return "afternoon"
        else:
            return "evening"
    
    def create_conversation_context(self, user_query: str, user_context: UserContext) -> Dict[str, Any]:
        """Create comprehensive conversation context"""
        
        # Analyze query intent
        intent = self._analyze_query_intent(user_query)
        
        # Determine conversation flow
        flow_type = self._map_intent_to_flow(intent)
        conversation_flow = self.conversation_flows.get(flow_type, self.conversation_flows["policy_inquiry"])
        
        # Check permissions
        permissions = self.role_permissions.get(user_context.role, self.role_permissions[UserRole.EMPLOYEE])
        
        return {
            "intent": intent,
            "conversation_flow": conversation_flow,
            "permissions": permissions,
            "personalization": self._get_personalization_settings(user_context),
            "context_awareness": self._build_context_awareness(user_context),
            "suggested_actions": self._generate_suggested_actions(intent, permissions)
        }
    
    def _analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """Analyze user query to understand intent"""
        query_lower = query.lower()
        
        intent_patterns = {
            "document_request": ["payslip", "form16", "certificate", "download", "send me"],
            "leave_inquiry": ["leave", "vacation", "time off", "balance", "apply"],
            "policy_question": ["policy", "rule", "procedure", "how to", "what is"],
            "attendance_query": ["attendance", "punch", "working hours", "late"],
            "benefits_inquiry": ["insurance", "medical", "benefit", "reimbursement"],
            "complaint": ["issue", "problem", "complaint", "not working", "error"]
        }
        
        detected_intents = []
        for intent_type, patterns in intent_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                detected_intents.append(intent_type)
        
        return {
            "primary_intent": detected_intents[0] if detected_intents else "general_inquiry",
            "secondary_intents": detected_intents[1:] if len(detected_intents) > 1 else [],
            "confidence": 0.9 if detected_intents else 0.3,
            "query_complexity": self._assess_query_complexity(query)
        }
    
    def _map_intent_to_flow(self, intent: Dict[str, Any]) -> str:
        """Map detected intent to conversation flow"""
        intent_flow_mapping = {
            "document_request": "document_request",
            "leave_inquiry": "leave_management",
            "policy_question": "policy_inquiry",
            "attendance_query": "policy_inquiry",
            "benefits_inquiry": "policy_inquiry",
            "complaint": "escalation_flow"
        }
        
        return intent_flow_mapping.get(intent["primary_intent"], "policy_inquiry")
    
    def _assess_query_complexity(self, query: str) -> str:
        """Assess the complexity of the user query"""
        word_count = len(query.split())
        question_marks = query.count('?')
        and_or_count = len(re.findall(r'\b(and|or|also|plus)\b', query.lower()))
        
        if word_count > 20 or question_marks > 2 or and_or_count > 2:
            return "high"
        elif word_count > 10 or question_marks > 1 or and_or_count > 0:
            return "medium"
        else:
            return "low"
    
    def _get_personalization_settings(self, user_context: UserContext) -> Dict[str, Any]:
        """Get personalization settings for the user"""
        return {
            "communication_style": "professional",
            "detail_level": "medium",
            "language_preference": user_context.preferences.get("language", "english"),
            "response_format": "html",
            "show_examples": user_context.role == UserRole.EMPLOYEE
        }
    
    def _build_context_awareness(self, user_context: UserContext) -> Dict[str, Any]:
        """Build context awareness from user history"""
        recent_interactions = user_context.interaction_history[-5:] if user_context.interaction_history else []
        
        return {
            "recent_topics": [interaction.get("topic") for interaction in recent_interactions],
            "frequent_requests": self._identify_frequent_requests(user_context.interaction_history),
            "last_session_context": user_context.current_session,
            "user_expertise_level": self._assess_user_expertise(user_context.interaction_history)
        }
    
    def _identify_frequent_requests(self, interaction_history: List[Dict[str, Any]]) -> List[str]:
        """Identify frequently requested items"""
        if not interaction_history:
            return []
        
        request_counts = {}
        for interaction in interaction_history:
            request_type = interaction.get("request_type")
            if request_type:
                request_counts[request_type] = request_counts.get(request_type, 0) + 1
        
        # Return top 3 frequent requests
        return sorted(request_counts.keys(), key=lambda x: request_counts[x], reverse=True)[:3]
    
    def _assess_user_expertise(self, interaction_history: List[Dict[str, Any]]) -> str:
        """Assess user's expertise level with the system"""
        if len(interaction_history) < 3:
            return "beginner"
        elif len(interaction_history) < 10:
            return "intermediate"
        else:
            return "advanced"
    
    def _generate_suggested_actions(self, intent: Dict[str, Any], permissions: Dict[str, Any]) -> List[str]:
        """Generate suggested actions based on intent and permissions"""
        suggestions = []
        
        if intent["primary_intent"] == "document_request":
            if "payslip" in permissions["can_request"]:
                suggestions.append("Request payslip for specific month")
            if "form16" in permissions["can_request"]:
                suggestions.append("Download Form16 for tax filing")
        
        elif intent["primary_intent"] == "leave_inquiry":
            suggestions.extend([
                "Check current leave balance",
                "View leave policy details",
                "Apply for new leave"
            ])
        
        return suggestions[:3]  # Limit to top 3 suggestions
