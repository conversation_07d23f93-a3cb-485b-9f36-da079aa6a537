# === Database Connection ===
from packages import *
def connect_to_mongo(host, port, user_name, pass_word, db_name):
    if "social" in user_name:
        return MongoClient("***************************************************************************************************")['jdsocial']
    return MongoClient(f'mongodb://{user_name}:{urllib.parse.quote_plus(pass_word)}@{host}:{port}')[db_name]

# Initialize database connection
try:
    db = connect_to_mongo("**************", 27017, "jdsocial", "jdsocial123", "jdsocial")
    chat_logs_collection = db['hr_chat_logs']
    chat_sessions_collection = db['hr_chat_sessions']
    logger.info("Database connection established successfully")
except Exception as e:
    logger.error(f"Database connection failed: {e}")
    db = None
    chat_logs_collection = None
    chat_sessions_collection = None