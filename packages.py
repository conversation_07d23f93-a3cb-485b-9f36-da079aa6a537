# Enhanced HR Agent with Proper Function Calling
import requests
import re
import operator
from typing import TypedDict, Annotated, List, Literal, Dict, Any
import pytz
import json
from datetime import datetime

from langchain_core.messages import AnyMessage, HumanMessage, AIMessage, ToolMessage
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import Tool<PERSON><PERSON>
from fastapi import FastAPI,Request
from pydantic import BaseModel

import jwt
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
import hashlib
import secrets
import uuid
from datetime import datetime,timedelta
from pymongo import MongoClient
import urllib.parse
from typing import Optional, List
import time

import sys
import os
from async_timeout import timeout
import time
import asyncio

import uvicorn
from dotenv import load_dotenv
load_dotenv()

from fastapi import FastAPI, Request, HTTPException,Header
from pydantic import BaseModel
from typing import List
import uuid
import requests
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams
from langchain_community.embeddings import HuggingFaceEmbeddings
import uvicorn
from sentence_transformers import CrossEncoder




