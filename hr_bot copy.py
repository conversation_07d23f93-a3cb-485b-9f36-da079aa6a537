from packages import *
import os
from dotenv import load_dotenv

# Load variables from .env
load_dotenv()
HISTORY_MAP = int(os.getenv("HISTORY_MAP", 10))  # default 10

# Import your existing modules
from tools_reg.cross_2 import *
from tools_reg.leave_test import *
from tools_reg.medical_test import *
from tools_reg.payslip import *
from tools_reg.buddy_ref import *
from tools_reg.attendance_test import *
from tools_reg.form16 import *
from tools_reg.reimbursement import *
from hr_date_validator import HRDateValidator, enhanced_payslip_validation_handler, enhanced_form16_validation_handler


import config

def set_emp_id(emp_id: int):
    print(f"EMP_ID updated to: {config.EMP_ID}")

    config.EMP_ID = emp_id
    print(f"EMP_ID updated to: {config.EMP_ID}")


import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# EMP_ID = None

from tools import *

# === Tool Definitions for Function Calling ===
from tool_schema import *
from packages import *
from validate_emp import *

# === Database Connection ===
def connect_to_mongo(host, port, user_name, pass_word, db_name):
    if "social" in user_name:
        return MongoClient("***************************************************************************************************")['jdsocial']
    return MongoClient(f'mongodb://{user_name}:{urllib.parse.quote_plus(pass_word)}@{host}:{port}')[db_name]

# Initialize database connection
try:
    db = connect_to_mongo("**************", 27017, "jdsocial", "jdsocial123", "jdsocial")
    chat_logs_collection = db['hr_chat_logs']
    chat_sessions_collection = db['hr_chat_sessions']
    logger.info("Database connection established successfully")
except Exception as e:
    logger.error(f"Database connection failed: {e}")
    db = None
    chat_logs_collection = None
    chat_sessions_collection = None

# Create a mapping of function names to actual functions
TOOL_FUNCTIONS = {
    "get_hr_context_tool": get_hr_context_tool,
    "get_leave_balance_tool": get_leave_balance_tool,
    "get_medical_insurance_tool": get_medical_insurance_tool,
    "get_buddy_ref_tool": get_buddy_ref_tool,
    "get_attendance_tool": get_attendance_tool,
    "get_reimbursement_tool": get_reimbursement_tool,
    "send_payslip_tool": send_payslip_tool,
    "send_form16_tool":send_form16_tool,
    # "send_form16_otp_tool": send_form16_otp_tool,
    # "validate_and_send_form16_tool": validate_and_send_form16_tool,
}

def generate_chat_heading(question: str, emp_context: dict) -> str:
    """Generate a descriptive heading for the chat session based on the first question."""
    try:
        # Use LLM to generate a concise heading
        llm = QwenChatLLM()
        
        heading_prompt = f"""
        Generate a short, descriptive heading (3-6 words) for this HR conversation:
        
        Employee Question: "{question}"
        Employee: {emp_context.get('empname', 'Employee')} - {emp_context.get('designation', '')}
        
        Examples of good headings:
        - "Leave Balance Inquiry"
        - "Payslip Request October"
        - "Medical Insurance Query"
        - "Form 16 Download"
        - "Attendance Issues"
        
        Return only the heading, nothing else.
        """
        
        response = llm([HumanMessage(content=heading_prompt)])
        heading = response.content.strip().strip('"').strip("'")
        
        # Fallback if LLM fails
        if not heading or len(heading) > 50:
            if "leave" in question.lower():
                heading = "Leave Related Query"
            elif "payslip" in question.lower():
                heading = "Payslip Request"
            elif "form 16" in question.lower() or "form16" in question.lower():
                heading = "Form 16 Request"
            elif "attendance" in question.lower():
                heading = "Attendance Query"
            elif "medical" in question.lower() or "insurance" in question.lower():
                heading = "Medical Insurance Query"
            elif "reimbursement" in question.lower():
                heading = "Reimbursement Query"
            else:
                heading = "HR Assistance"
        
        return heading
    except Exception as e:
        logger.error(f"Error generating chat heading: {e}")
        return "HR Query"

def check_session_exists(session_id: str) -> dict:
    """Check if session exists in database."""
    if chat_sessions_collection is None:
        return {"exists": False, "session_data": None}
    
    try:
        session_data = chat_sessions_collection.find_one({"session_id": session_id})
        return {"exists": bool(session_data), "session_data": session_data}
    except Exception as e:
        logger.error(f"Error checking session: {e}")
        return {"exists": False, "session_data": None}

def create_new_session(session_id: str, emp_code: str, heading: str, user_ip: str) -> bool:
    """Create a new chat session."""
    if chat_sessions_collection is None:
        return False
    
    try:
        session_doc = {
            "session_id": session_id,
            "emp_code": emp_code,
            "heading": heading,
            "created_at": datetime.now(),
            "user_ip": user_ip,
            "last_activity": datetime.now()
        }
        
        chat_sessions_collection.insert_one(session_doc)
        logger.info(f"New session created: {session_id}")
        return True
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        return False

def log_chat_interaction(session_id: str, emp_code: str, heading: str, user_question: str, 
                        bot_response: str, response_time: float, status_code: int, user_ip: str,error_code: int = None,error_msg:str=None):
    
 
    """Log each chat interaction."""
    if chat_logs_collection is None:
        return
    
    try:
        log_doc = {
            "session_id": session_id,
            "emp_code": emp_code,
            "heading": heading,
            "user_question": user_question,
            "bot_response": bot_response,
            "response_time_seconds": response_time,
            "timestamp": datetime.now(),
            "created_at": datetime.now(),
            "user_ip": user_ip,
            "status_code": status_code,
            "error_code":error_code,
            "error_msg":error_msg
        }
        
        chat_logs_collection.insert_one(log_doc)
        
        # Update session last activity
        if chat_sessions_collection is not None:
            chat_sessions_collection.update_one(
                {"session_id": session_id},
                {"$set": {"last_activity": datetime.now()}}
            )
        
        logger.info(f"Chat interaction logged for session: {session_id}")
    except Exception as e:
        logger.error(f"Error logging chat interaction: {e}")


# === Enhanced QwenChatLLM with Function Calling ===
LLM_URL=os.getenv("LLM_URL")
LLM_MODEL=os.getenv("LLM_MODEL")

class QwenChatLLM:
    def __init__(self, model: str = LLM_MODEL, base_url: str = LLM_URL):
        self.model = model
        self.base_url = base_url
        self.max_retries = 3
        self.retry_delay = 1.0

    def __call__(self, messages: List, tools: List = None, **kwargs):
        """Make a call to the LLM with optional function calling."""
        openai_msgs = []
        for m in messages:
            if hasattr(m, 'content'):
                content = m.content
            else:
                content = str(m)
                
            if isinstance(m, HumanMessage):
                openai_msgs.append({"role": "user", "content": content})
            elif isinstance(m, AIMessage):
                openai_msgs.append({"role": "assistant", "content": content})
            else:
                openai_msgs.append({"role": "user", "content": content})

        payload = {
            "model": self.model,
            "messages": openai_msgs,
            "temperature": 0,
        }
        
        # Add tools if provided
        if tools:
            payload["tools"] = tools
            payload["tool_choice"] = "auto"

        try:
            resp = requests.post(f"{self.base_url}", json=payload, timeout=60)
            resp.raise_for_status()
            result = resp.json()
            
            print("******************************")
            print("LLM Response:", json.dumps(result, indent=2)[:1000] + "..." if len(str(result)) > 1000 else result)
            print("******************************")
            
            choice = result['choices'][0]
            message = choice['message']
            
            # Handle function calls
            if message.get('tool_calls'):
                return self._create_function_call_response(message)
            else:
                content = message['content']
                if content and "</think>" in content:
                    content = content.split("</think>")[-1].strip()
                return MockAIMessage(content=content)
                
        except Exception as e:
            print(f"LLM call failed: {e}")
            return MockAIMessage(content="I'm experiencing technical difficulties. Please try again.")

    def _create_function_call_response(self, message):
        """Create a response object for function calls."""
        return MockAIMessageWithTools(
            content=message.get('content', ''),
            tool_calls=message['tool_calls']
        )

# Mock classes to handle responses
class MockAIMessage:
    def __init__(self, content: str):
        self.content = content
        self.tool_calls = None

class MockAIMessageWithTools:
    def __init__(self, content: str, tool_calls: List):
        self.content = content
        self.tool_calls = tool_calls

# === Agent State ===
class AgentState(TypedDict):
    messages: Annotated[List[AnyMessage], operator.add]
    employee_context: dict
    payslip_state: dict

# === Enhanced call_model function ===
def call_model(state: AgentState):
    now = datetime.now()
    ist_tz = pytz.timezone('Asia/Kolkata')
    now_ist = now.astimezone(ist_tz) if now.tzinfo else ist_tz.localize(now)
    
    emp_context = state.get("employee_context", {})
    
    # Check if we have tool results to process
    tool_msgs = [m for m in state["messages"] if isinstance(m, ToolMessage)]
    
    if tool_msgs:
        # Process tool results - same as your existing logic
        tool_results_text = []
        for tm in tool_msgs:
            label = tm.name.replace("_tool", "").replace("get_", "").replace("_", " ").title()
            content = tm.content
            tool_results_text.append(f"{label}:\n{content}")
        
        tool_results_block = "\n\n".join(tool_results_text)
        
        user_msg = None
        for m in reversed(state["messages"]):
            if isinstance(m, HumanMessage):
                user_msg = m
                break



        final_system = f"""
You are JIA – Justdial Intelligent Assistant. 
Your job is to give short, clear, professional answers to employees based on the tool results below.

Employee Context:
{emp_context}

=============================
🌍 MULTILINGUAL RULES
=============================
- Detect the language of the user message: "{user_msg.content if user_msg else ''}"
- Reply in the SAME language and script style (English / Hindi / Tamil / Telugu / Marathi / Bengali / Gujarati / Kannada / Malayalam / Punjabi).
- If user writes in regional language but English script, respond in the same style.
- No emojis, symbols, or casual slang.
- Keep the style consistent.

Examples:
- User: "muje na payslip chahiye" → Response: "Aapka payslip bhej diya gaya hai"
- User: "naku leave balance kavali" → Response: "Mee leave balance idi..."
- User: "I need my leave balance" → Response: "Your leave balance is..."

=============================
🔒 SECURITY RULES
=============================
- Do NOT expose internal codes, APIs, raw data, or system messages.
- Show ONLY clean, user-friendly information.

=============================
✅ RESPONSE RULES
=============================
- Answer ONLY what user asked, nothing extra.
- Use tool results data to provide accurate information  
- direct to the point(1–2 sentences) max, short and crisp.
- Be accurate and professional.
- Keep language style same as user. 
- Ignore tool details that are not directly related to the question  
- Maintain user's language style  

### WHAT NOT TO DO:
❌ Don't expose internal data (raw API data), codes, IDs, technical details and raw status codes
❌ Don't use emojis or casual language or extra information
❌ Don't mention technical details or system codes
RESPOND in the SAME language and script style as the user's message. Keep it short and direct. Do not include codes, technical details.

""" 

## 🔥 CRITICAL: RESPONSE LENGTH in ≤40 words (extend to max 80 if needed)  

# - Keep answers very short: max 40 words  
# - If absolutely necessary, extend up to 80 words but only if required for clarity  

        # print(final_system)
        # print("&&&&&&&&&&&&&&&&&&&&&&&&")
        messages = []
        if user_msg:
            messages.append(user_msg)
        # print(messages)
        # print("&&&&&&&&&&&&&&&&&&&&&&&&&")
        messages.append(HumanMessage(content=f"Tool Results Data:\n{tool_results_block}\n\nBased on this data, please answer my question above. and RESPOND in the SAME language and script style as the user's message. Keep it short and direct. Do not include codes, technical details."))

        llm = QwenChatLLM()
        ai_msg_content = llm(messages, system_prompt=final_system).content
        return {"messages": [AIMessage(content=ai_msg_content)]}

    # Initial processing with function calling
    context_info = f"""
Employee Context:
- Name: {emp_context.get("empname", "")} (Employee ID: {emp_context.get("empcode", "")})
- Designation: {emp_context.get("designation", "")}
- Department: {emp_context.get("department", "")}
- Location: {emp_context.get("city", "")}
- Email: {emp_context.get("email_id", "")}
- Reporting Manager: {emp_context.get("reporting_head", "")}
"""
    # Financial year calculation
    year_fy = now.year
    month_fy = now.month

    
    if month_fy >= 4:  # April or later
        fy = f"Financial Year: {year_fy}-{year_fy+1} (April 1, {year_fy} to March 31, {year_fy+1})"
    else:  # Jan–Mar
        fy = f"Financial Year: {year_fy-1}-{year_fy} (April 1, {year_fy-1} to March 31, {year_fy})"


    financial_year_str=fy


    system_prompt = f'''
You are JIA (Justdial Intelligent Assistant), an advanced HR assistant designed to help employees with all HR-related queries efficiently and accurately.

## 📋 SYSTEM CONTEXT
📅 Current Date: {now.strftime('%B %d, %Y at %H:%M IST')}
📅 Current Financial Year: {financial_year_str}
🏢 Organization: Justdial
👤 Employee Context: {emp_context}

## 🎯 CORE IDENTITY & MISSION
- **Primary Role**: Professional HR support for Justdial employees
- **Personality**: Helpful, efficient, accurate, and professional
- **Communication Style**: Direct, clear, respectful
- **Language Support**: English, Hindi, and other Indian languages with appropriate transliteration

## 🛡️ FUNDAMENTAL SECURITY & COMPLIANCE RULES

### RULE #1: HR DOMAIN RESTRICTION
- **SCOPE**: Only HR, payroll, benefits, policies, leave, attendance, insurance, reimbursements
- **BLOCK**: Technical queries, personal advice, general knowledge, math problems, coding
- **REDIRECT**: "I'm your HR assistant and can only help with employee-related matters. What can I assist you with regarding your HR needs?"

### RULE #2: NO TECHNICAL EXPOSURE
- Never mention tool names, APIs, or internal system details
- Use: "Let me check that information for you" instead of exposing backend processes
- Maintain professional facade at all times


### RULE #3: ZERO ASSUMPTIONS POLICY
- Never assume user intent or missing information
- Always validate before proceeding
- Ask specific questions when information is incomplete
- Never hallucinate responses or claim actions not performed
- **NEVER CALL TOOLS WITHOUT ALL REQUIRED PARAMETERS EXPLICITLY PROVIDED BY USER**

### RULE #4: NO FORMATTING
- NEVER use emojis, HTML, markdown, or special characters
- Use only plain text responses
- Keep communication clean and professional


## 🚨 CRITICAL TOOL CALLING RESTRICTIONS

### ABSOLUTE PROHIBITION: NO PREMATURE TOOL CALLS
**YOU MUST NEVER:**
1. Call send_payslip_tool or send_form16_tool without ALL parameters confirmed
2. Assume missing information like dates, form types, or email preferences
3. Call tools based on partial information or assumptions
4. Skip validation steps under any circumstances

### MANDATORY VALIDATION CHECKPOINT
**BEFORE ANY TOOL CALL:**
- ALL required parameters must be explicitly provided by user
- NO assumptions about missing data
- NO hallucination of parameter values
- NO calling tools with partial information

## 🔧 INTELLIGENT TOOL ORCHESTRATION

### 📊 Tool Selection Matrix

You have access to HR tools to help employees. Use intelligent judgment to determine when tools can provide helpful information:

**get_hr_context_tool** - Use for:
-For HR policies, processes, guidelines, holidays, benefits, insurance procedures, claim processes, who to contact, how-to guides (automatically includes employee context)
- Any question about company policies, procedures, or guidelines
- Process-related questions (how to apply for something, who to contact, etc.)
- General information about benefits, holidays, company rules
- When you need context about HR processes or policies
- Insurance claim procedures and general benefit information

**get_leave_balance_tool** - Use when:
- For personal leave balance information
- Employee asks about their leave status, balance, or remaining days
- Questions about leave availability or leave-related numbers
- Any inquiry that would benefit from knowing their current leave situation

**get_medical_insurance_tool** - Use when:
- Employee asks about their insurance coverage or policy details
- Questions about dependents, coverage amounts, policy numbers
- Personal insurance-related inquiries (not claim procedures)
-For employee's personal medical insurance coverage details, policy numbers, family members covered - NOT for claim processes

**get_attendance_tool** - Use when:
- Employee asks about their attendance, working hours, or presence records
- Questions about PIPO(punch-in/out) times, absent days, LOP details
- Any attendance-related inquiry that needs their personal data
- For attendance records, present/absent days, working hours, LOP (Loss of Pay) details,PIPO(PunchIN /Punch out) details,leaves (PIPO, sick/casual) and any all data related to atttendance

**get_buddy_ref_tool** - Use when:
- Employee asks about referrals, buddy system, or referral rewards
- Questions about people they referred or referral status
- For employee referral information, buddy system data, referral bonus status, and referral history

**get_reimbursement_tool** - Use when:
- Employee asks about FINANCIAL reimbursements ONLY (travel, fuel, medical expenses, etc.)
- For general questions: call without months parameter
- For specific period questions: call with months parameter
- For employee's reimbursement claims status, fuel allowance, conveyance, mobile/internet, education, leave travel allowance (LTA), and any reimbursement-related information
- DO NOT use for: MRF, approvals, recruitment, non-financial processes

**send_payslip_tool**
- **Use When**: Employee explicitly requests payslip delivery
- **Validation Required**: Date (past/current months only), email (registered only)
- **Process**: Validate → Confirm → Execute → Report result

**send_form16_tool**
- **Use When**: Employee explicitly requests Form16 delivery  
- **Validation Required**: Financial year (completed only), form type, email (registered only)
- **Process**: Validate → Confirm → Execute → Report result

## 🔐 BULLETPROOF VALIDATION SYSTEM

### 📅 DATE VALIDATION ENGINE

**CURRENT DATE CONTEXT**: {now.strftime('%B %d, %Y')}
**CURRENT FINANCIAL YEAR**: {financial_year_str}

**PAYSLIP VALIDATION LOGIC**:
- VALID: All months up to current month/year only
- INVALID: Future months that haven't occurred
- REJECTION MESSAGE: "I can only provide payslips for past or current months. Please specify a valid month/year."

**FORM16 VALIDATION LOGIC**:
- VALID: Only completed financial years (ended before current FY)
- INVALID: Current ongoing FY and future FYs
- REJECTION MESSAGE: "I can only provide Form16 for completed financial years. The current financial year {financial_year_str} is ongoing. Please specify a completed year."

### 📧 EMAIL SECURITY PROTOCOL

**REGISTERED EMAIL POLICY**:
- ALLOW: Only official_email and personal_email from employee context
- BLOCK: All external/unregistered email addresses
- OPTIONS: "official email", "personal email", "both"
- REJECTION MESSAGE: "For security reasons, I can only send documents to your registered email addresses. Which would you prefer: official email or personal email?"

## 🎭 MANDATORY VALIDATION SEQUENCES

### 📝 FORM16 REQUEST VALIDATION SEQUENCE

**STEP 1: Financial Year Validation**
```
IF user requests current FY or future FY:
    STOP → Reject with explanation about completed FYs only
IF user requests valid completed FY:
    PROCEED to Step 2
IF FY not specified:
    ASK: "Which completed financial year do you need Form16 for?"
```

**STEP 2: Form Type Validation**
```
IF form type not specified:
    STOP → ASK: "Which Form16 do you need: Part A, Part B, Tax Computation, or All Parts?"
IF form type specified:
    PROCEED to Step 3
```

**STEP 3: Email Validation**
```
IF email preference not specified:
    STOP → ASK: "Where should I send it: official email, personal email, or both?"
IF valid email specified:
    PROCEED to Step 4
```

**STEP 4: Tool Execution**
```
ONLY after ALL validations pass:
    Use send_form16_tool with validated parameters
    Report actual tool result (never assume success)
```

### 📄 PAYSLIP REQUEST VALIDATION SEQUENCE

**STEP 1: Date Validation**
```
IF requested date is future:
    STOP → Reject with explanation about past/current months only
IF date is valid:
    PROCEED to Step 2
IF date not specified:
    ASK: "Which month and year do you need the payslip for?"
```

**STEP 2: Email Validation**
```
IF email preference not specified:
    STOP → ASK: "Where should I send it: official email, personal email, or both?"
IF valid email specified:
    PROCEED to Step 3
```

**STEP 3: Tool Execution**
```
ONLY after ALL validations pass:
    Use send_payslip_tool with validated parameters
    Report actual tool result (never assume success)
```

## 🚫 ABSOLUTE PROHIBITIONS

**NEVER DO THESE ACTIONS UNDER ANY CIRCUMSTANCES:**
1. Skip validation steps for any reason
2. Assume missing information (form type, email, dates)
3. Claim documents were sent without actually using tools
4. Accept current ongoing FY for Form16 requests
5. Send documents to unregistered email addresses
6. Respond to vague confirmations like "ok" with assumptions
7. Use emojis, HTML, or special formatting
8. Expose tool names or technical details to users
9. Call send_payslip_tool or send_form16_tool without explicit user confirmation of ALL parameters
10. **CALL ANY DOCUMENT TOOLS WITH PARTIAL OR ASSUMED INFORMATION**

## 🎯 CRITICAL DECISION FLOW

### FOR DOCUMENT REQUESTS:
```
User Request → Check if ALL parameters provided → 
IF NOT: Ask for missing parameters, DO NOT CALL TOOLS →
IF YES: Validate each parameter →
IF INVALID: Reject with explanation →
IF VALID: THEN AND ONLY THEN call appropriate tool
```

### FOR INFORMATION REQUESTS:
```
User Request → Determine relevant information tool →
Call appropriate get_* tool →
Present information clearly
```
## 🗣️ MULTILINGUAL COMMUNICATION PROTOCOL

### LANGUAGE DETECTION & RESPONSE
- If user writes in Hindi script → Respond in Hindi script
- If user uses Hinglish → Respond in Hinglish style
- If user mixes languages → Mirror their style
- Always use appropriate honorifics (ji, sir/madam) when suitable
- Maintain professional tone across all languages

### RESPECTFUL COMMUNICATION EXAMPLES
- English: "How can I assist you today?"
- Hindi: "Aaj main aapki kya madad kar sakta hun?"
- Hinglish: "Aaj main aapki kya help kar sakta hun?"

## 🎯 RESPONSE GENERATION FRAMEWORK

### STANDARD RESPONSE STRUCTURE

**FOR INFORMATION QUERIES**:
1. Acknowledge the request professionally
2. Use appropriate tool to fetch information
3. Present information clearly in plain text
4. Offer relevant follow-up assistance

**FOR DOCUMENT REQUESTS**:
1. **NEVER CALL TOOLS IMMEDIATELY**
2. Start validation sequence immediately
3. Ask for missing information one by one
4. Only proceed when ALL validations pass and ALL parameters explicitly provided
5. Report actual tool results honestly

**FOR INVALID REQUESTS**:
1. Politely decline with clear explanation
2. Suggest valid alternatives
3. Redirect to proper HR channels if needed
4. Maintain helpful attitude

### RESPONSE QUALITY STANDARDS
- **CLARITY**: Use simple, direct language without jargon
- **ACCURACY**: Only provide verified information from tools
- **COMPLETENESS**: Address all aspects of user's query
- **PROFESSIONALISM**: Maintain corporate communication standards
- **SECURITY**: Never compromise on validation requirements

## 🚨 ERROR HANDLING PROTOCOLS

### TOOL FAILURE HANDLING
```
IF any tool fails or returns error:
    RESPOND: "I'm unable to retrieve that information right now. Please try again later or contact HR directly."
    NEVER: Make up responses or assume tool worked
```

### AMBIGUOUS QUERY HANDLING
```
IF user query has multiple interpretations:
    ASK: "I can help with several things. Are you looking for: [list specific options]?"
    WAIT: For user clarification before proceeding
```

### INAPPROPRIATE REQUEST HANDLING
```
IF query is outside HR domain:
    RESPOND: "I'm your HR assistant and can only help with employee-related matters. What can I assist you with regarding your HR needs?"
    
IF security concern detected:
    RESPOND: "I cannot process that request for security reasons. Please contact HR directly."
```

## 🔄 CONVERSATION FLOW EXAMPLES

### PERFECT FORM16 CONVERSATION
```
User: "I need Form16"
AI: "I can help you with Form16. Please provide:
1. Which completed financial year?
2. Which parts: Part A, Part B, Tax Computation, or All Parts?  
3. Send to which email: official email or personal email?"

User: "2023-24 all parts to official email"
AI: [Validates all parameters → Uses tool → Reports actual result]
"Your Form16 for FY 2023-24 (all parts) has been sent to your official email."
```

### PERFECT PAYSLIP CONVERSATION
```
User: "Send me payslip"
AI: "I can help you with your payslip. Please provide:
1. Which month and year?
2. Send to which email: official email or personal email?"

User: "August 2024 to both emails"
AI: [Validates date and email → Uses tool → Reports actual result]
"Your payslip for August 2024 has been sent to both your official and personal emails."
```

### HANDLING INVALID REQUESTS
```
User: "Send Form16 for current year"
AI: "I cannot provide Form16 for the current financial year {financial_year_str} as it's still ongoing. I can only provide Form16 for completed financial years. Please specify a completed year."

User: "Send payslip for next month"  
AI: "I can only provide payslips for past or current months. Please specify a valid month/year."
```

## 📊 QUALITY CONTROL CHECKLIST

Before every response, verify:
- ✅ Is this query HR-related?
- ✅ Have I validated all required parameters?
- ✅ Am I using the correct tool for this query?
- ✅ Is my response accurate and based on actual tool results?
- ✅ Have I maintained professional tone without formatting?
- ✅ Am I not exposing any technical details?
- ✅ Am I not making any assumptions about missing information?
- ✅ Have I followed the mandatory validation sequence?

## 🎪 CONVERSATION STATE MANAGEMENT

### CONTEXT TRACKING RULES
- Remember conversation context within current session
- Track partially completed requests (missing parameters)
- Never carry over assumptions from previous exchanges
- Reset validation requirements for each new document request

### FOLLOW-UP INTELLIGENCE
- After successful information retrieval, ask if user needs related help
- For document requests, confirm successful delivery
- Suggest relevant services based on user's query pattern
- Always maintain professional helpful attitude

## 🏆 SUCCESS CRITERIA

A successful interaction must:
- Resolve user query accurately using appropriate tools
- Maintain strict security and compliance standards  
- Provide professional communication experience
- Minimize unnecessary back-and-forth through clear questions
- Leave user satisfied with helpful, accurate information
- Never compromise on validation requirements
- **NEVER CALL DOCUMENT TOOLS WITHOUT EXPLICIT USER CONFIRMATION OF ALL PARAMETERS**
- Report honest results without hallucination

## 🎯 CRITICAL REMINDERS

**ALWAYS REMEMBER**:
1. **VALIDATION IS MANDATORY, NOT OPTIONAL - ESPECIALLY FOR DOCUMENT TOOLS**
2. **NEVER CALL send_payslip_tool OR send_form16_tool WITHOUT ALL PARAMETERS**
3. Better to ask too many questions than make wrong assumptions
4. Security compliance is more important than user convenience
5. Honest "I don't know" is better than hallucinated responses
6. Professional communication reflects company values
7. Every interaction represents Justdial's HR excellence
8.Be genuinely helpful and professional and direct
9. Never skip steps to speed up conversation
9. **BE GENUINELY HELPFUL AND PROFESSIONAL BUT NEVER COMPROMISE ON VALIDATION**


**FINAL CHECKPOINT**: Before using any send_payslip_tool or send_form16_tool, ensure ALL validation steps are completed and ALL parameters are explicitly confirmed by the user. NO EXCEPTIONS.
**CRITICAL DECISION POINT**: If user provides incomplete information for document requests, you MUST ask for missing details. You MUST NOT call tools with partial information or assumptions.

Remember: You are the digital face of Justdial's HR department. Every interaction should exemplify professionalism, accuracy, and genuine employee care while maintaining absolute security compliance.NEVER compromise on validation requirements.
'''

    system_prompt=f"""
You are JIA (Justdial Intelligent Assistant), the official HR assistant for Justdial employees designed to help employees with all HR-related queries efficiently and accurately.

## IDENTITY ENFORCEMENT - CRITICAL
- You are JIA, NOT Qwen or any other AI
- You work for Justdial company exclusively  
- You are the digital HR representative
- NEVER mention being developed by Alibaba Cloud or any other company
- Always identify as "JIA" when asked about your name

## SYSTEM CONTEXT
📅 Current Date: {now.strftime('%B %d, %Y at %H:%M IST')}
📅 Current Financial Year: {financial_year_str}
👤 Employee: {emp_context.get("empname", "")} ({emp_context.get("empcode", "")})
📧 Official Email: {emp_context.get("official_email", "")}
📧 Personal Email: {emp_context.get("personal_email", "")}

## CORE RULES
1. **HR Domain Only**: Only handle HR/payroll/benefits/leave/attendance queries
2. **Language Matching**: Respond in the SAME language/script as user's message
3. **Security**: Never expose technical details, tool names, or internal codes
4. **Accuracy**: Only provide verified information, never assume or hallucinate
5. **JIA Identity**: Always maintain JIA identity, never mention other AI systems

## PERSONALITY
- Helpful and professional
- Knowledgeable about Justdial HR policies
- Friendly but not overly casual
- Efficient and direct in responses

## TOOL USAGE INTELLIGENCE
- **get_hr_context_tool**: HR policies, procedures, guidelines, benefits info
- **get_leave_balance_tool**: Personal leave balance and status
- **get_medical_insurance_tool**: Personal insurance coverage details
- **get_attendance_tool**: Attendance records, PIPO times, LOP details
- **get_buddy_ref_tool**: Referral information and bonus status
- **get_reimbursement_tool**: Financial reimbursement claims status

## CRITICAL DOCUMENT REQUEST PROTOCOL
**NEVER call send_payslip_tool or send_form16_tool directly from this function**
- Document requests are handled by pre-validation logic
- If user requests documents, ask for missing parameters
- Only information tools should be called from here

## RESPONSE STYLE
- Professional and direct (20-40 words typically)
- Mirror user's language exactly
- No emojis, formatting, or technical jargon
- Address exactly what user asked

- Always maintain JIA identity

Remember: You are JIA, the dedicated HR assistant for Justdial employees. Your job is to make their HR experience smooth and efficient."""

    # print(system_prompt)

    # Get recent conversation for context
    print("state[messages]**************",state["messages"])
    recent_messages = state["messages"][-HISTORY_MAP:] if len(state["messages"]) > HISTORY_MAP else state["messages"]
    context=f"""
        
You are JIA (Justdial Intelligent Assistant), the official HR assistant for Justdial employees designed to help employees with all HR-related queries efficiently and accurately.

                ## 📋 SYSTEM CONTEXT
        📅 Current Date: {now.strftime('%B %d, %Y at %H:%M IST')}
        📅 Current Financial Year: {financial_year_str}
        🏢 Organization: Justdial
        👤 Employee Context: {emp_context}

    """
    conversation_msgs = [HumanMessage(content=context)]

    # conversation_msgs = []
    for msg in recent_messages:
        if isinstance(msg, (HumanMessage, AIMessage)):
            conversation_msgs.append(msg)
    print("**********************",conversation_msgs)

    try:
        llm = QwenChatLLM()
        response = llm(conversation_msgs, tools=TOOLS_SCHEMA,system_prompt=system_prompt)
        
        # Check if response contains function calls
        if hasattr(response, 'tool_calls') and response.tool_calls:
            # Return the response with tool calls - the router will handle them
            return {"messages": [AIMessage(content=response.content or "", 
                                          additional_kwargs={"tool_calls": response.tool_calls})]}
        else:
            return {"messages": [AIMessage(content=response.content)]}
            
    except Exception as e:
        logger.error(f"LLM call failed: {e}")
        fallback_response = "I'm experiencing technical difficulties. Please try again or contact HR directly."
        return {"messages": [AIMessage(content=fallback_response)]}

# === Enhanced tool router ===
def tool_router(state: AgentState):
    last_msg = state["messages"][-1]
    emp_context = state.get("employee_context", {})
    tool_messages = []
    
    # Check for function calls in additional_kwargs
    if hasattr(last_msg, 'additional_kwargs') and 'tool_calls' in last_msg.additional_kwargs:
        tool_calls = last_msg.additional_kwargs['tool_calls']
        
        for tool_call in tool_calls:
            function_name = tool_call['function']['name']
            function_args = json.loads(tool_call['function']['arguments'])
            tool_call_id = tool_call['id']
            
            print(f"Executing function: {function_name} with args: {function_args}")
            
            if function_name in TOOL_FUNCTIONS:
                try:
                    # Special handling for hr_context tool to pass emp_context
                    if function_name == "get_hr_context_tool":
                        function_args['emp_context'] = emp_context
                    
                    # Execute the function
                    result = TOOL_FUNCTIONS[function_name](**function_args)
                    
                    tool_messages.append(
                        ToolMessage(
                            name=function_name,
                            content=str(result),
                            tool_call_id=tool_call_id
                        )
                    )
                      
                    # # Check if result indicates no data found or error
                    # if isinstance(result, str) and any(phrase in result.lower() for phrase in 
                    #     ["no data found", "error", "not found", "no records", "no information available"]):
                    #     # Return a user-friendly message instead of raw error
                    #     friendly_msg = "I don't have specific information about that. Please connect with the HR team for assistance."
                    #     tool_messages.append(
                    #         ToolMessage(
                    #             name=function_name,
                    #             content=friendly_msg,
                    #             tool_call_id=tool_call_id
                    #         )
                    #     )
                    # else:
                    #     tool_messages.append(
                    #         ToolMessage(
                    #             name=function_name,
                    #             content=str(result),
                    #             tool_call_id=tool_call_id
                    #         )
                    #     )
                    
                except Exception as e:
                    error_msg = f"Error executing {function_name}: {str(e)}"
                    print(error_msg)
                    tool_messages.append(
                        ToolMessage(
                            name=function_name,
                            content=error_msg,
                            tool_call_id=tool_call_id
                        )
                    )
    
    # Fallback: Check for old-style TOOL_CALL patterns in content
    elif hasattr(last_msg, 'content') and "TOOL_CALL" in last_msg.content:
        # Keep your existing regex patterns as fallback
        patterns = [
            ("get_leave_balance_tool", r"TOOL_CALL\s*:\s*get_leave_balance\((\d{4,})?\)", 
             lambda empcode: get_leave_balance_tool(empcode), "leave_{}"),
            ("get_hr_context_tool", r"TOOL_CALL\s*:\s*get_hr_context\(([^)]+)\)", 
             lambda question: get_hr_context_tool(question.strip().strip('"\''), emp_context), "hr_{}"),
            ("get_medical_insurance_tool", r"TOOL_CALL\s*:\s*get_medical_insurance\((\d{4,})?\)", 
             lambda empcode: get_medical_insurance_tool(empcode), "med_{}"),
            ("get_buddy_ref_tool", r"TOOL_CALL\s*:\s*get_buddy_ref\((\d{4,})?\)", 
             lambda empcode: get_buddy_ref_tool(empcode), "bud_{}"),
        ]
        
        for tool_name, pattern, func, call_id_fmt in patterns:
            matches = re.findall(pattern, last_msg.content)
            for param in matches:
                try:
                    result = func(param if param else None)
                    tool_messages.append(
                        ToolMessage(
                            name=tool_name, 
                            content=str(result), 
                            tool_call_id=call_id_fmt.format(param or "default")
                        )
                    )
                except Exception as e:
                    tool_messages.append(
                        ToolMessage(
                            name=tool_name,
                            content=f"Error: {str(e)}",
                            tool_call_id=call_id_fmt.format(param or "error")
                        )
                    )
    
    print(f"Tool messages generated: {[tm.name for tm in tool_messages]}")
    return {"messages": tool_messages} if tool_messages else {"messages": []}

def should_continue(state: AgentState) -> Literal["tools", "__end__"]:
    last_message = state["messages"][-1]
    
    # Check for function calls
    if hasattr(last_message, 'additional_kwargs') and 'tool_calls' in last_message.additional_kwargs:
        return "tools"
    
    # Check for old-style tool calls
    if hasattr(last_message, 'content'):
        if "TOOL_CALL" in last_message.content:
            return "tools"
        elif "NO_TOOL" in last_message.content:
            last_message.content = "I do not have this information. Please connect with the HR team."
            return "__end__"
    
    return "__end__"

# === Build the graph ===
graph = StateGraph(AgentState)
graph.add_node("agent", call_model)
graph.add_node("tools", tool_router)
graph.set_entry_point("agent")
graph.add_conditional_edges("agent", should_continue, {"tools": "tools", "__end__": END})
graph.add_edge('tools', 'agent')
app = graph.compile()

# === Employee Validation (unchanged) ===

# === FastAPI Implementation (unchanged structure) ===

fastapi_app = FastAPI(title="ECare")

from fastapi.middleware.cors import CORSMiddleware

# Add the CORS middleware
# fastapi_app.add_middleware(
#     CORSMiddleware,
#     allow_origins=["*"],
#     allow_credentials=True,
#     allow_methods=["*"],  # Allow all HTTP methods (GET, POST, etc.)
#     allow_headers=["*"],
#         expose_headers=["*"]
# # Allow all headers
# )


fastapi_app.add_middleware(
    CORSMiddleware,
    allow_origins=[

        "*"  # Allow all origins (use cautiously in production)
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Authorization",
        "Content-Type",
        "Accept",
        "Origin",
        "User-Agent",
        "DNT",
        "Cache-Control",
        "X-Mx-ReqToken",
        "Keep-Alive",
        "X-Requested-With",
        "If-Modified-Since",
        "X-CSRF-Token"
    ],
    expose_headers=["*"]
)
class SuppressPrints:
    def __enter__(self):
        self._stdout = sys.stdout  # Save the original stdout
        sys.stdout = open(os.devnull, 'w')  # Redirect stdout to os.devnull

    def __exit__(self, exc_type, exc_value, traceback):
        sys.stdout.close()  # Close the redirected stdout
        sys.stdout = self._stdout  # Restore the original stdout



from routes.apis import *




if __name__ == "__main__":
    api_port=os.getenv("API_PORT")

    # with SuppressPrints():
    uvicorn.run(fastapi_app, host='0.0.0.0', port=int(api_port), proxy_headers=True, forwarded_allow_ips='*') # THIS LINE)



