"""
Enhanced HR Data Models and Schema
Provides structured data models for comprehensive HR operations
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime, date
from enum import Enum
from dataclasses import dataclass, field
from pydantic import BaseModel, Field, validator
import json
import uuid

class EmployeeStatus(Enum):
    """Employee status types"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ON_LEAVE = "on_leave"
    TERMINATED = "terminated"
    SUSPENDED = "suspended"

class LeaveType(Enum):
    """Types of leave"""
    SICK_LEAVE = "sick_leave"
    CASUAL_LEAVE = "casual_leave"
    EARNED_LEAVE = "earned_leave"
    MATERNITY_LEAVE = "maternity_leave"
    PATERNITY_LEAVE = "paternity_leave"
    EMERGENCY_LEAVE = "emergency_leave"
    COMPENSATORY_OFF = "compensatory_off"

class DocumentType(Enum):
    """Types of HR documents"""
    PAYSLIP = "payslip"
    FORM16 = "form16"
    APPOINTMENT_LETTER = "appointment_letter"
    EXPERIENCE_CERTIFICATE = "experience_certificate"
    SALARY_CERTIFICATE = "salary_certificate"
    RELIEVING_LETTER = "relieving_letter"

class RequestStatus(Enum):
    """Status of HR requests"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

# Pydantic Models for API Validation
class EmployeeProfile(BaseModel):
    """Comprehensive employee profile model"""
    emp_id: str = Field(..., description="Unique employee identifier")
    emp_code: str = Field(..., description="Employee code")
    personal_info: Dict[str, Any] = Field(default_factory=dict)
    employment_info: Dict[str, Any] = Field(default_factory=dict)
    contact_info: Dict[str, Any] = Field(default_factory=dict)
    emergency_contacts: List[Dict[str, Any]] = Field(default_factory=list)
    
    class Config:
        schema_extra = {
            "example": {
                "emp_id": "EMP001",
                "emp_code": "JD001",
                "personal_info": {
                    "first_name": "John",
                    "last_name": "Doe",
                    "date_of_birth": "1990-01-01",
                    "gender": "Male",
                    "marital_status": "Single"
                },
                "employment_info": {
                    "designation": "Software Engineer",
                    "department": "Technology",
                    "join_date": "2023-01-01",
                    "employee_type": "Full Time",
                    "reporting_manager": "EMP002"
                },
                "contact_info": {
                    "official_email": "<EMAIL>",
                    "personal_email": "<EMAIL>",
                    "mobile": "+91-9876543210",
                    "address": "123 Main St, City"
                }
            }
        }

class LeaveBalance(BaseModel):
    """Leave balance model"""
    emp_id: str
    financial_year: str
    leave_balances: Dict[str, float] = Field(default_factory=dict)
    leave_history: List[Dict[str, Any]] = Field(default_factory=list)
    last_updated: datetime = Field(default_factory=datetime.now)
    
    class Config:
        schema_extra = {
            "example": {
                "emp_id": "EMP001",
                "financial_year": "2024-2025",
                "leave_balances": {
                    "sick_leave": 12.0,
                    "casual_leave": 8.0,
                    "earned_leave": 15.0
                },
                "leave_history": [
                    {
                        "leave_type": "sick_leave",
                        "start_date": "2024-01-15",
                        "end_date": "2024-01-16",
                        "days_taken": 2.0,
                        "status": "approved"
                    }
                ]
            }
        }

class PayrollInfo(BaseModel):
    """Payroll information model"""
    emp_id: str
    salary_structure: Dict[str, float] = Field(default_factory=dict)
    deductions: Dict[str, float] = Field(default_factory=dict)
    benefits: Dict[str, float] = Field(default_factory=dict)
    tax_info: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        schema_extra = {
            "example": {
                "emp_id": "EMP001",
                "salary_structure": {
                    "basic_salary": 50000.0,
                    "hra": 20000.0,
                    "special_allowance": 15000.0,
                    "gross_salary": 85000.0
                },
                "deductions": {
                    "pf": 6000.0,
                    "esi": 850.0,
                    "tds": 5000.0
                },
                "benefits": {
                    "medical_insurance": 5000.0,
                    "meal_vouchers": 2000.0
                }
            }
        }

class HRRequest(BaseModel):
    """Generic HR request model"""
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    emp_id: str
    request_type: str
    request_data: Dict[str, Any] = Field(default_factory=dict)
    status: RequestStatus = RequestStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    processed_by: Optional[str] = None
    comments: List[Dict[str, Any]] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True

class DocumentRequest(HRRequest):
    """Document request model"""
    document_type: DocumentType
    delivery_method: str = "email"
    delivery_address: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True
        schema_extra = {
            "example": {
                "emp_id": "EMP001",
                "request_type": "document_request",
                "document_type": "payslip",
                "delivery_method": "email",
                "delivery_address": "<EMAIL>",
                "parameters": {
                    "month": "2024-01",
                    "format": "pdf"
                }
            }
        }

class AttendanceRecord(BaseModel):
    """Attendance record model"""
    emp_id: str
    date: date
    time_in: Optional[datetime] = None
    time_out: Optional[datetime] = None
    working_hours: float = 0.0
    status: str = "absent"  # present, absent, half_day, short_leave
    location: Optional[str] = None
    remarks: Optional[str] = None
    
    class Config:
        schema_extra = {
            "example": {
                "emp_id": "EMP001",
                "date": "2024-01-15",
                "time_in": "2024-01-15T09:00:00",
                "time_out": "2024-01-15T18:00:00",
                "working_hours": 8.0,
                "status": "present",
                "location": "office"
            }
        }

class ConversationLog(BaseModel):
    """Conversation log model for chat history"""
    session_id: str
    emp_id: str
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.now)
    user_message: str
    bot_response: str
    intent_detected: Optional[str] = None
    tools_used: List[str] = Field(default_factory=list)
    response_time: float = 0.0
    satisfaction_score: Optional[int] = None
    
    class Config:
        schema_extra = {
            "example": {
                "session_id": "sess_123",
                "emp_id": "EMP001",
                "user_message": "What is my leave balance?",
                "bot_response": "Your current leave balance is...",
                "intent_detected": "leave_inquiry",
                "tools_used": ["get_leave_balance_tool"],
                "response_time": 2.5
            }
        }

# Dataclasses for Internal Processing
@dataclass
class HRWorkflow:
    """Workflow definition for HR processes"""
    workflow_id: str
    name: str
    description: str
    steps: List[Dict[str, Any]]
    triggers: List[str]
    approvers: List[str]
    sla_hours: int
    auto_escalation: bool = False

@dataclass
class PolicyRule:
    """HR policy rule definition"""
    rule_id: str
    policy_area: str
    rule_description: str
    conditions: List[Dict[str, Any]]
    actions: List[Dict[str, Any]]
    exceptions: List[Dict[str, Any]] = field(default_factory=list)
    effective_date: date = field(default_factory=date.today)
    expiry_date: Optional[date] = None

@dataclass
class AnalyticsMetric:
    """Analytics metric for HR insights"""
    metric_id: str
    metric_name: str
    metric_type: str  # count, percentage, average, etc.
    value: Union[int, float, str]
    period: str
    filters: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

# Database Schema Definitions
HR_DATABASE_SCHEMA = {
    "employees": {
        "indexes": [
            {"fields": ["emp_id"], "unique": True},
            {"fields": ["emp_code"], "unique": True},
            {"fields": ["official_email"], "unique": True},
            {"fields": ["department", "status"]},
            {"fields": ["reporting_manager"]}
        ],
        "validation": {
            "required": ["emp_id", "emp_code", "empname", "department"],
            "properties": {
                "emp_id": {"type": "string", "pattern": "^EMP[0-9]+$"},
                "status": {"enum": ["active", "inactive", "terminated"]}
            }
        }
    },
    
    "leave_balances": {
        "indexes": [
            {"fields": ["emp_id", "financial_year"], "unique": True},
            {"fields": ["last_updated"]},
            {"fields": ["emp_id"]}
        ],
        "validation": {
            "required": ["emp_id", "financial_year"],
            "properties": {
                "leave_balances": {"type": "object"},
                "financial_year": {"pattern": "^[0-9]{4}-[0-9]{4}$"}
            }
        }
    },
    
    "hr_requests": {
        "indexes": [
            {"fields": ["request_id"], "unique": True},
            {"fields": ["emp_id", "status"]},
            {"fields": ["request_type", "created_at"]},
            {"fields": ["status", "created_at"]}
        ],
        "validation": {
            "required": ["request_id", "emp_id", "request_type"],
            "properties": {
                "status": {"enum": ["pending", "approved", "rejected", "completed"]}
            }
        }
    },
    
    "conversation_logs": {
        "indexes": [
            {"fields": ["session_id", "timestamp"]},
            {"fields": ["emp_id", "timestamp"]},
            {"fields": ["intent_detected"]},
            {"fields": ["timestamp"]}
        ],
        "ttl": {"field": "timestamp", "expireAfterSeconds": 7776000}  # 90 days
    },
    
    "attendance_records": {
        "indexes": [
            {"fields": ["emp_id", "date"], "unique": True},
            {"fields": ["date"]},
            {"fields": ["emp_id", "status"]}
        ],
        "validation": {
            "required": ["emp_id", "date"],
            "properties": {
                "status": {"enum": ["present", "absent", "half_day", "short_leave"]}
            }
        }
    }
}

# Response Templates
STRUCTURED_RESPONSES = {
    "leave_balance": {
        "template": {
            "employee_id": "{emp_id}",
            "employee_name": "{emp_name}",
            "financial_year": "{fy}",
            "leave_balances": {
                "sick_leave": "{sick_balance}",
                "casual_leave": "{casual_balance}",
                "earned_leave": "{earned_balance}"
            },
            "total_available": "{total_balance}",
            "last_updated": "{last_updated}",
            "next_accrual_date": "{next_accrual}"
        }
    },
    
    "document_status": {
        "template": {
            "request_id": "{request_id}",
            "document_type": "{doc_type}",
            "status": "{status}",
            "requested_at": "{request_time}",
            "delivery_method": "{delivery_method}",
            "delivery_address": "{delivery_address}",
            "estimated_delivery": "{eta}",
            "tracking_info": "{tracking}"
        }
    },
    
    "policy_information": {
        "template": {
            "policy_area": "{policy_area}",
            "policy_title": "{title}",
            "summary": "{summary}",
            "key_points": ["{point1}", "{point2}", "{point3}"],
            "effective_date": "{effective_date}",
            "last_updated": "{last_updated}",
            "related_policies": ["{related1}", "{related2}"],
            "contact_for_queries": "{contact_info}"
        }
    }
}
