import secrets
import os
from cryptography.fernet import Fernet
import jwt
import datetime

# ============================================================
# Setup encryption keys (JWT + Fernet)
# ============================================================
def setup_encryption_keys():
    # JWT secret
    jwt_secret = os.getenv("JWT_SECRET_KEY")
    if not jwt_secret:
        jwt_secret = secrets.token_urlsafe(32)
        os.environ["JWT_SECRET_KEY"] = jwt_secret
        print(f"🔑 Generated JWT_SECRET_KEY: {jwt_secret}")
    
    # Fernet encryption key
    encryption_key = os.getenv("ENCRYPTION_KEY")
    if not encryption_key:
        encryption_key = Fernet.generate_key().decode()
        os.environ["ENCRYPTION_KEY"] = encryption_key
        print(f"🔐 Generated ENCRYPTION_KEY: {encryption_key}")
    
    return jwt_secret, encryption_key

# Initialize keys
SECRET_KEY, ENCRYPTION_KEY = setup_encryption_keys()
cipher = Fernet(ENCRYPTION_KEY.encode())


# ============================================================
# Test Fernet Encryption / Decryption
# ============================================================
def test_encryption_decryption():
    message = "Hello, this is a test message!"
    print(f"\n📩 Original Message: {message}")

    # Encrypt
    encrypted = cipher.encrypt(message.encode())
    print(f"🔒 Encrypted: {encrypted}")

    # Decrypt
    decrypted = cipher.decrypt(encrypted).decode()
    print(f"🔓 Decrypted: {decrypted}")

    assert message == decrypted, "Decryption failed!"
    print("✅ Encryption/Decryption test passed!")


# ============================================================
# Test JWT Token Generation / Decoding
# ============================================================
def test_jwt():
    payload = {
        "empcode": "EMP12345",
        "exp": datetime.datetime.utcnow() + datetime.timedelta(minutes=5)  # expires in 5 minutes
    }

    # Generate JWT
    token = jwt.encode(payload, SECRET_KEY, algorithm="HS256")
    print(f"\n🔑 Generated JWT: {token}")

    # Decode JWT
    decoded = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
    print(f"📜 Decoded JWT: {decoded}")

    assert decoded["empcode"] == payload["empcode"], "JWT decoding failed!"
    print("✅ JWT encode/decode test passed!")


# ============================================================
# Run tests
# ============================================================
if __name__ == "__main__":
    test_encryption_decryption()
    # test_jwt()
