from packages import *
def generate_chat_heading(question: str, emp_context: dict) -> str:
    """Generate a descriptive heading for the chat session based on the first question."""
    try:
        # Use LLM to generate a concise heading
        llm = QwenChatLLM()
        
        heading_prompt = f"""
        Generate a short, descriptive heading (3-6 words) for this HR conversation:
        
        Employee Question: "{question}"
        Employee: {emp_context.get('empname', 'Employee')} - {emp_context.get('designation', '')}
        
        Examples of good headings:
        - "Leave Balance Inquiry"
        - "Payslip Request October"
        - "Medical Insurance Query"
        - "Form 16 Download"
        - "Attendance Issues"
        
        Return only the heading, nothing else.
        """
        
        response = llm([HumanMessage(content=heading_prompt)])
        heading = response.content.strip().strip('"').strip("'")
        
        # Fallback if LLM fails
        if not heading or len(heading) > 50:
            if "leave" in question.lower():
                heading = "Leave Related Query"
            elif "payslip" in question.lower():
                heading = "Payslip Request"
            elif "form 16" in question.lower() or "form16" in question.lower():
                heading = "Form 16 Request"
            elif "attendance" in question.lower():
                heading = "Attendance Query"
            elif "medical" in question.lower() or "insurance" in question.lower():
                heading = "Medical Insurance Query"
            elif "reimbursement" in question.lower():
                heading = "Reimbursement Query"
            else:
                heading = "HR Assistance"
        
        return heading
    except Exception as e:
        logger.error(f"Error generating chat heading: {e}")
        return "HR Query"