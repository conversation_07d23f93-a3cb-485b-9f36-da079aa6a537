# === Enhanced Tools with Proper Function Definitions ===
from packages import *
import config
from tools_reg.cross_2 import *
from tools_reg.leave_test import *
from tools_reg.medical_test import *
from tools_reg.payslip import *
from tools_reg.buddy_ref import *
from tools_reg.attendance_test import *
from tools_reg.form16 import *
from tools_reg.reimbursement import *
from tools_reg.payout import *
from tools_reg.mrf import *


# === Utility function for month generation ===
def generate_months_for_period(period_text: str) -> List[str]:
    """Generate month list based on natural language input"""
    from dateutil.relativedelta import relativedelta
    
    current_date = datetime.now()
    current_month_str = current_date.strftime("%Y-%m")
    
    # Extract number if present
    numbers = re.findall(r'\d+', period_text.lower())
    num = int(numbers[0]) if numbers else 1
    
    period_lower = period_text.lower()
    months = []
    
    if "current month" in period_lower or "this month" in period_lower:
        months = [current_month_str]
    elif "last month" in period_lower or "previous month" in period_lower:
        last_month = current_date - relativedelta(months=1)
        months = [last_month.strftime("%Y-%m")]
    elif "months" in period_lower:
        # Generate last N months including current
        for i in range(num):
            month_date = current_date - relativedelta(months=i)
            months.append(month_date.strftime("%Y-%m"))
    else:
        # Default to current month
        months = [current_month_str]
    
    return months

def get_hr_context_tool(question: str, emp_context: dict = None) -> str:
    """Get HR context and policies information."""
    if emp_context:
        context_info = []
        if emp_context.get('department'):
            context_info.append(f"Department: {emp_context['department']}")
        if emp_context.get('city'):
            context_info.append(f"Location: {emp_context['city']}")
        
        if context_info:
            enhanced_question = f"{question}. Employee context: {', '.join(context_info)}"
        else:
            enhanced_question = question
    else:
        enhanced_question = question
    
    print("--- HR RAG Tool Invoked ---", enhanced_question)
    context, _ = hr_documnet_chat(enhanced_question)
    print(context)
    return context

def get_leave_balance_tool(emp_code: str = None) -> str:
    """Get employee leave balance information."""
    #global EMP_ID
    
    if config.EMP_ID:
        code = config.EMP_ID
    else:
        if not emp_code or not re.search(r'\b\d{4,}\b', emp_code):
            return "Please provide a valid employee code (at least 4 digits)."
        match = re.search(r'\b\d{4,}\b', emp_code)
        code = match.group(0)
    
    print(f"--- Leave API Tool Invoked for emp_code: {code} ---")
    result = leave_api(code)
    print(result)
    return result

def get_payout_tool(emp_code: str = None,months: List[str] = None) -> str:
    """Get employee leave balance information."""
    #global EMP_ID
    
    if config.EMP_ID:
        code = config.EMP_ID
    else:
        if not emp_code or not re.search(r'\b\d{4,}\b', emp_code):
            return "Please provide a valid employee code (at least 4 digits)."
        match = re.search(r'\b\d{4,}\b', emp_code)
        code = match.group(0)
    
    print(f"--- payout API Tool Invoked for emp_code: {code} ---")
    result = payout_api(code)
    print(result)
    return result

def get_medical_insurance_tool(emp_code: str = None) -> str:
    """Get employee medical insurance details."""
    #global EMP_ID
    
    if config.EMP_ID:
        empcode = config.EMP_ID
    else:
        empcode = emp_code
    
    print("--- Medical Insurance Tool Invoked ---")
    result = medical_api(empcode)
    print("Medical result:", result)
    return str(result)

def get_buddy_ref_tool(emp_code: str = None) -> str:
    """Get employee buddy referral information."""
    #global EMP_ID
    
    if config.EMP_ID:
        empcode = config.EMP_ID
    else:
        empcode = emp_code
    
    print("--- Buddy Referral Tool Invoked ---")
    result = buddy_ref_api(empcode)
    print("Buddy ref result:", result)
    return str(result)



def filter_future_dates(date_list):
    current = datetime.now()
    filtered = []
    
    for date_str in date_list:
        # Parse the date string
        date_obj = datetime.strptime(date_str, '%Y-%m')
        
        # Compare with current date (only year and month)
        if date_obj.year < current.year or (date_obj.year == current.year and date_obj.month <= current.month):
            filtered.append(date_str)
    
    return filtered

# Using the more robust method
def get_attendance_tool(emp_code: str = None, months: List[str] = None) -> str:
    """Get employee attendance information for specified months."""
    #global EMP_ID
    context = """
Attendance Management System Context:

## Attendance Status Types:
- "present": Employee worked full day with adequate hours
Employee came late to office (after 11 AM) but still worked. This is NOT considered as leave, NO LOP deduction, and IS counted as a working day
- "half day": Employee worked approximately half the required hours and in office less than 6 hrs
- "absent": Employee did not attend work (may have approved leave)
- "W/O": Week-off/Weekend day
- "holiday": Public/company holiday

## Time and Hours Fields:
- time_in: Clock-in time (YYYY-MM-DD HH:MM:SS)
- time_out: Clock-out time (YYYY-MM-DD HH:MM:SS)
- working_hrs: Total working MINUTES (not hours) for the day
- req_time_in/req_time_out: Requested/corrected punch times
- og_emp_timing: Official shift timing (e.g., "10-19" = 10 AM to 7 PM)
- status: "present" | "absent" | "half day" | "short leave"
  * "short leave" = Late login (after 11 AM) but COUNTS AS WORKING DAY
- req_status: "0"=pending, "1"=approved
- reason: Request reason (e.g., "Wfh" = Work from Home)

## IMPORTANT: working_hrs is in MINUTES
- To convert to hours: working_hrs ÷ 60
- Example: working_hrs: 540 = 9 hours
- Standard work day is typically 9 hours (540 minutes)
Monthly Target: 180 hours should be completed in a month


## Request and Approval System:
- req_status: Request approval status ("1" = approved)
- hod_status: Head of Department approval status
- req_type: Type of request (SELF for employee-initiated)
- reason: Employee's reason (e.g., "Missed punching", "Wfh", "Sick")

## Summary Fields:
- tot_working: Total working time in readable format
- total_present_days: Days employee was present
- total_absent_days: Total absent days
- net_working_days: Total working days in period
- no_of_days_sl: Sick leave days taken
- no_of_days_pl: Privilege leave days taken
- lop: Loss of Pay days
- approved_request: List of approved WFH/time requests
- pending_request: List of pending requests
- absent_days: List of absent days
- short_fall_hrs: Hours deficit
- total_present_days: Days present
- missing_punches: Incomplete punch records

PUNCH DATA (biometric):
- Actual punch times from biometric system OR mobile app (WFH)
- Multiple entries per day are normal

Key Business Rules:

Short Leave Policy: Coming late to office (after 11 AM) results in "short leave" status but:

It's NOT considered as actual leave
NO Loss of Pay (LOP) deduction
IS counted as a working day
Employee still needs to complete their working hours


Monthly Target: Employees must complete 180 working hours per month regardless of attendance status (as long as it's a working day)
Working Day Classification:

"present", "short leave", and "half day" are all considered working days
Only "absent" (without approved leave) may result in LOP

EY POINTS:
- working_hrs is in MINUTES (divide by 60 for hours)
- "short leave" = proper working day (just late arrival)
- Punch data can be from office biometric OR mobile app during WFH
- Punch data can be from office biometric OR mobile app during WFH OR manual punch in/out
- Login/logout hours alone are NOT leaves - they are attendance records
- Actual LEAVES will appear as separate fields in API response if taken and wih proper reason and apporval status

Attendance API Response:
"""
    context = """
Attendance Management System Context:

## Attendance Status Types:
- "present": Employee worked full day with adequate hours
- "short leave": Employee came late to office (after 11 AM) but still worked. This is NOT considered as leave, NO LOP deduction, and IS counted as a working day
- "half day": Employee worked approximately half the required hours and in office less than 6 hrs
- "absent": Employee did not attend work (may have approved leave)
- "W/O": Week-off/Weekend day
- "holiday": Public/company holiday

## Time and Hours Fields:
- time_in: Clock-in time (YYYY-MM-DD HH:MM:SS)
- time_out: Clock-out time (YYYY-MM-DD HH:MM:SS)
- working_hrs: Total working MINUTES (not hours) for the day
- req_time_in/req_time_out: Requested/corrected punch times
- og_emp_timing: Official shift timing (e.g., "10-19" = 10 AM to 7 PM)

## IMPORTANT: working_hrs is in MINUTES
- To convert to hours: working_hrs ÷ 60
- Example: working_hrs: 540 = 9 hours
- Standard work day is typically 9 hours (540 minutes)
- Monthly Target: 180 hours should be completed in a month

## Request and Approval System:
- req_status: Request approval status ("0" = pending, "1" = approved)
- hod_status: Head of Department approval status ("0" = pending, "1" = approved)
- req_type: Type of request (SELF for employee-initiated)
- reason: Employee's reason (e.g., "Missed punching", "Wfh", "Sick")

## Summary Fields:
- tot_working: Total working time in readable format
- total_present_days: Days employee was present
- total_absent_days: Total absent days
- net_working_days: Total working days in period
- no_of_days_sl: Sick leave days taken
- no_of_days_pl: Privilege leave days taken
- lop: Loss of Pay days
- approved_request: List of approved WFH/time requests
- pending_request: List of pending requests
- absent_days: List of absent days
- short_fall_hrs: Hours deficit
- missing_punches: Incomplete punch records

## Punch Data (biometric):
- Actual punch times from biometric system OR mobile app (WFH) OR manual punch in/out
- Multiple entries per day are normal

## Key Business Rules:

### Short Leave Policy:
Coming late to office (after 11 AM) results in "short leave" status but:
- It's NOT considered as actual leave
- NO Loss of Pay (LOP) deduction
- IS counted as a working day
- Employee still needs to complete their working hours

### Monthly Target:
Employees must complete 180 working hours per month regardless of attendance status (as long as it's a working day)

### Working Day Classification:
- "present", "short leave", and "half day" are all considered working days
- Only "absent" (without approved leave) may result in LOP

## KEY POINTS:
- working_hrs is in MINUTES (divide by 60 for hours)
- "short leave" = proper working day (just late arrival)
- Punch data can be from office biometric OR mobile app during WFH OR manual punch in/out
- Login/logout hours alone are NOT leaves - they are attendance records
- Actual LEAVES will appear as separate fields in API response if taken with proper reason and approval status
Attendance API Response:

"""
    if config.EMP_ID:
        empcode = config.EMP_ID
    else:
        empcode = emp_code
    
    print(f"--- Attendance Tool Invoked for empcode: {empcode} ---")
    
    # Generate months if not provided but period_text is given
    if not months and period_text:
        months = generate_months_for_period(period_text)
    elif not months:
        months = [datetime.now().strftime("%Y-%m")]
    
    print(f"Fetching attendance for months: {months}")
    
    try:
        months = filter_future_dates(months)

        months = sorted(months, key=lambda x: datetime.strptime(x, "%Y-%m"))
        
        limit = True
        if len(months) > 4:
            actual_count = len(months)
            months = months[-4:]
            limit = False
        
        attendance_data = get_employee_attendance(empcode, months)
        formatted_response = []
        
        for month, data in attendance_data.items():
            month_name = datetime.strptime(month, "%Y-%m").strftime("%B %Y")
            
            if "error" in data:
                formatted_response.append(f"\n**{month_name}**: Error - {data['error']}")
                continue
            
            try:
                att_info = data.get("data", {}).get("data", [{}])[0].get("attendance", {})
                
                if not att_info:
                    formatted_response.append(f"\n**{month_name}**: No attendance data available")
                    continue
                
                att_info=json.dumps(att_info)
                
                month_summary = f"""
                **{month_name}**:
                • Attendance Raw data: {att_info}
                """
                formatted_response.append(month_summary)
                
            except Exception as e:
                formatted_response.append(f"\n**{month_name}**: Error parsing data - {str(e)}")
        
        if formatted_response:
            # result=context
            result = context+"\n\n\n".join(formatted_response)
            if not limit:
                result += f"\n\n\nIMP NOTE to tell in reply to user: You requested the last {actual_count} months, but I can only extract data for the latest 4 months."
            return result
        else:
            return "No attendance data found for the requested period."
            
    except Exception as e:
        return f"Error fetching attendance data: {str(e)}"


def send_payslip_tool(emp_code: str = None, months: List[str] = None, email_type: str = None) -> str:
    """Send payslip to employee email."""
    #global EMP_ID
    
    if config.EMP_ID:
        empcode = config.EMP_ID
    else:
        empcode = emp_code
    
    print(f"--- Payslip Tool Invoked for empcode: {empcode} ---")
    
    if not months:
        months = [datetime.now().strftime("%Y-%m")]
    
    try:
        months = sorted(months, key=lambda x: datetime.strptime(x, "%Y-%m"))
        result = payslip_api(empcode, months, email_type)
        return result
    except Exception as e:
        print(e)
        return "Error occurred in sending pay slips"

# def send_form16_otp_tool(emp_code: str = None, years: List[str] = None) -> str:
#     """Send OTP for Form 16 request."""
#     #global EMP_ID
    
#     if EMP_ID:
#         empcode = EMP_ID
#     else:
#         empcode = emp_code
    
#     print(f"--- Form16 OTP Tool Invoked for empcode: {empcode}, years: {years} ---")
#     return "Send Form16 OTP: OTP sent to your registered mobile"

# def validate_and_send_form16_tool(emp_code: str = None, years: List[str] = None, otp: str = None, email_type: str = None) -> str:
#     """Validate OTP and send Form 16."""
#     #global EMP_ID
    
#     if EMP_ID:
#         empcode = EMP_ID
#     else:
#         empcode = emp_code
    
#     print(f"--- Form16 Validation Tool Invoked for empcode: {empcode} ---")
#     return f"Form16 validated and sent via {email_type} email"
def send_form16_tool(emp_code: str = None, years: List[str] = None,form_types :List[str] = None, email_type: str = None) -> str:
    """Validate OTP and send Form 16."""
    #global EMP_ID
    
    if config.EMP_ID:
        empcode = config.EMP_ID
    else:
        empcode = emp_code
        
    print(f"--- fomr16 Tool Invoked for empcode: {empcode} {years}  {form_types} {email_type}---")
    
    # if not months:
    #     months = [datetime.now().strftime("%Y-%m")]
    
    try:
        """
    Sorts financial years (YYYY-YYYY) before sending Form16 API requests.
    Example: ["2023-2024", "2021-2022", "2022-2023"] → ["2021-2022","2022-2023","2023-2024"]
    """
    # Sort by the starting year
        years = sorted(years, key=lambda x: datetime.strptime(x.split("-")[0], "%Y"))

        print("Sorted years:", years)
        result = form16_api(empcode, years, form_types, email_type)
       
        return result#"sendt sucess form 16"
    except Exception as e:
        print(e)
        return "Error occurred in sending form16 formas"
        
  
def get_reimbursement_tool(emp_code: str = None, months: List[str] = None, period_text: str = None) -> str:
    """Get employee reimbursement information for specified months."""
    #global EMP_ID
    
    if config.EMP_ID:
        empcode = config.EMP_ID
    else:
        empcode = emp_code
    
    print(f"--- reimbursement Tool Invoked for empcode: {empcode} ---")
    
    # Generate months if not provided but period_text is given
    if not months and period_text:
        months = generate_months_for_period(period_text)
    elif not months:
        months = [datetime.now().strftime("%Y-%m")]
    
    print(f"Fetching reimbursement for months: {months}")
    
    try:
        months = sorted(months, key=lambda x: datetime.strptime(x, "%Y-%m"))
        
        limit = True
        if len(months) > 4:
            actual_count = len(months)
            months = months[-4:]
            limit = False
        
        reimbursement_data = reimbursement_api(empcode, months)
        formatted_response = []
        
        return reimbursement_data
        
     
    except Exception as e:
        return f"Error fetching attendance data: {str(e)}"
    
    
    



def get_mrf_tool(emp_code: str = None) -> str:
    """Get employee leave balance information."""
    #global EMP_ID
    
    if config.EMP_ID:
        code = config.EMP_ID
    else:
        if not emp_code or not re.search(r'\b\d{4,}\b', emp_code):
            return "Please provide a valid employee code (at least 4 digits)."
        match = re.search(r'\b\d{4,}\b', emp_code)
        code = match.group(0)
    
    print(f"--- MRF API Tool Invoked for emp_code: {code} ---")
    result = mrf_api(code)
    print(result)
    return result