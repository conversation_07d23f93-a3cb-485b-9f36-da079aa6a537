import secrets
from cryptography.fernet import Fernet
import os
# Quick fix for missing keys
def setup_encryption_keys():
    jwt_secret = os.getenv("JWT_SECRET_KEY")
    if not jwt_secret:
        jwt_secret = secrets.token_urlsafe(32)
        os.environ["JWT_SECRET_KEY"] = jwt_secret
        print(f"🔑 Generated JWT_SECRET_KEY: {jwt_secret}")
    
    encryption_key = os.getenv("ENCRYPTION_KEY")
    if not encryption_key:
        encryption_key = Fernet.generate_key().decode()
        os.environ["ENCRYPTION_KEY"] = encryption_key
        print(f"🔐 Generated ENCRYPTION_KEY: {encryption_key}")
    
    return jwt_secret, encryption_key

# Initialize keys
SECRET_KEY, ENCRYPTION_KEY = setup_encryption_keys()
cipher = Fernet(ENCRYPTION_KEY.encode())