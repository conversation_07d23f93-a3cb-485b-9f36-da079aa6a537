"""
Advanced Agentic HR System
Provides autonomous task execution, proactive monitoring, and intelligent routing
"""

from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
import asyncio
import json
import logging
from abc import ABC, abstractmethod

class AgentCapability(Enum):
    """Types of agentic capabilities"""
    AUTONOMOUS_EXECUTION = "autonomous_execution"
    PROACTIVE_MONITORING = "proactive_monitoring"
    INTELLIGENT_ROUTING = "intelligent_routing"
    PREDICTIVE_ASSISTANCE = "predictive_assistance"
    WORKFLOW_ORCHESTRATION = "workflow_orchestration"
    COMPLIANCE_MONITORING = "compliance_monitoring"

class TaskPriority(Enum):
    """Priority levels for autonomous tasks"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class TaskStatus(Enum):
    """Status of autonomous tasks"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class AutonomousTask:
    """Definition of an autonomous task"""
    task_id: str
    task_type: str
    description: str
    priority: TaskPriority
    scheduled_time: datetime
    employee_id: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

@dataclass
class ProactiveAlert:
    """Proactive alert definition"""
    alert_id: str
    alert_type: str
    title: str
    message: str
    severity: str  # info, warning, critical
    target_employees: List[str]
    trigger_conditions: Dict[str, Any]
    created_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    acknowledged_by: List[str] = field(default_factory=list)

class HRAgent(ABC):
    """Abstract base class for HR agents"""
    
    @abstractmethod
    async def execute(self, task: AutonomousTask) -> Dict[str, Any]:
        """Execute the agent's task"""
        pass
    
    @abstractmethod
    def can_handle(self, task_type: str) -> bool:
        """Check if agent can handle the task type"""
        pass

class DocumentDeliveryAgent(HRAgent):
    """Agent for autonomous document delivery"""
    
    def __init__(self, hr_tools):
        self.hr_tools = hr_tools
        self.supported_tasks = ["payslip_delivery", "form16_delivery", "certificate_delivery"]
    
    def can_handle(self, task_type: str) -> bool:
        return task_type in self.supported_tasks
    
    async def execute(self, task: AutonomousTask) -> Dict[str, Any]:
        """Execute document delivery task"""
        try:
            task_type = task.task_type
            params = task.parameters
            
            if task_type == "payslip_delivery":
                result = await self._deliver_payslip(params)
            elif task_type == "form16_delivery":
                result = await self._deliver_form16(params)
            else:
                raise ValueError(f"Unsupported task type: {task_type}")
            
            return {"success": True, "result": result}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _deliver_payslip(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Deliver payslip autonomously"""
        emp_id = params.get("emp_id")
        month = params.get("month")
        email_type = params.get("email_type", "official_email")
        
        # Use existing payslip tool
        result = self.hr_tools.send_payslip_tool(
            emp_code=emp_id,
            months=[month],
            email_type=email_type
        )
        
        return {"delivery_status": "completed", "details": result}
    
    async def _deliver_form16(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Deliver Form16 autonomously"""
        emp_id = params.get("emp_id")
        year = params.get("financial_year")
        form_types = params.get("form_types", ["part_a", "part_b"])
        email_type = params.get("email_type", "official_email")
        
        # Use existing form16 tool
        result = self.hr_tools.send_form16_tool(
            emp_code=emp_id,
            years=[year],
            form_types=form_types,
            email_type=email_type
        )
        
        return {"delivery_status": "completed", "details": result}

class ComplianceMonitoringAgent(HRAgent):
    """Agent for monitoring HR compliance"""
    
    def __init__(self, hr_tools, db_connection):
        self.hr_tools = hr_tools
        self.db = db_connection
        self.supported_tasks = ["attendance_compliance", "leave_compliance", "document_compliance"]
    
    def can_handle(self, task_type: str) -> bool:
        return task_type in self.supported_tasks
    
    async def execute(self, task: AutonomousTask) -> Dict[str, Any]:
        """Execute compliance monitoring task"""
        try:
            task_type = task.task_type
            
            if task_type == "attendance_compliance":
                result = await self._monitor_attendance_compliance()
            elif task_type == "leave_compliance":
                result = await self._monitor_leave_compliance()
            elif task_type == "document_compliance":
                result = await self._monitor_document_compliance()
            else:
                raise ValueError(f"Unsupported task type: {task_type}")
            
            return {"success": True, "result": result}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _monitor_attendance_compliance(self) -> Dict[str, Any]:
        """Monitor attendance compliance issues"""
        # Check for employees with low attendance
        current_month = datetime.now().strftime("%Y-%m")
        
        # This would integrate with your attendance system
        compliance_issues = []
        
        # Example compliance check
        # attendance_data = self.hr_tools.get_attendance_tool(months=[current_month])
        # Process attendance data to find compliance issues
        
        return {
            "compliance_status": "monitored",
            "issues_found": len(compliance_issues),
            "issues": compliance_issues
        }

class ProactiveAssistanceAgent(HRAgent):
    """Agent for providing proactive assistance"""
    
    def __init__(self, hr_tools, ml_predictor=None):
        self.hr_tools = hr_tools
        self.ml_predictor = ml_predictor
        self.supported_tasks = ["predict_needs", "suggest_actions", "remind_deadlines"]
    
    def can_handle(self, task_type: str) -> bool:
        return task_type in self.supported_tasks
    
    async def execute(self, task: AutonomousTask) -> Dict[str, Any]:
        """Execute proactive assistance task"""
        try:
            task_type = task.task_type
            
            if task_type == "predict_needs":
                result = await self._predict_employee_needs(task.employee_id)
            elif task_type == "suggest_actions":
                result = await self._suggest_actions(task.employee_id)
            elif task_type == "remind_deadlines":
                result = await self._remind_deadlines()
            else:
                raise ValueError(f"Unsupported task type: {task_type}")
            
            return {"success": True, "result": result}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _predict_employee_needs(self, emp_id: str) -> Dict[str, Any]:
        """Predict what an employee might need"""
        predictions = []
        
        # Example predictions based on patterns
        current_date = datetime.now()
        
        # Check if it's near month-end (payslip time)
        if current_date.day >= 25:
            predictions.append({
                "type": "document_need",
                "item": "payslip",
                "confidence": 0.8,
                "reason": "Month-end approaching"
            })
        
        # Check if it's tax season
        if current_date.month in [3, 4]:
            predictions.append({
                "type": "document_need",
                "item": "form16",
                "confidence": 0.9,
                "reason": "Tax filing season"
            })
        
        return {"predictions": predictions}
    
    async def _suggest_actions(self, emp_id: str) -> Dict[str, Any]:
        """Suggest actions based on employee context"""
        suggestions = []
        
        # Get employee leave balance
        try:
            leave_data = self.hr_tools.get_leave_balance_tool(emp_code=emp_id)
            # Parse leave data and suggest actions
            suggestions.append({
                "type": "leave_planning",
                "message": "Consider planning your remaining leaves",
                "priority": "medium"
            })
        except Exception:
            pass
        
        return {"suggestions": suggestions}
    
    async def _remind_deadlines(self) -> Dict[str, Any]:
        """Send deadline reminders"""
        reminders = []
        current_date = datetime.now()
        
        # Tax filing deadline reminder
        if current_date.month == 7 and current_date.day <= 31:
            reminders.append({
                "type": "tax_deadline",
                "message": "ITR filing deadline approaching (July 31st)",
                "urgency": "high"
            })
        
        return {"reminders": reminders}

class HRAgenticOrchestrator:
    """Main orchestrator for agentic HR capabilities"""
    
    def __init__(self, hr_tools, db_connection):
        self.hr_tools = hr_tools
        self.db = db_connection
        self.agents = []
        self.task_queue = []
        self.running_tasks = {}
        self.completed_tasks = []
        self.logger = logging.getLogger(__name__)
        
        # Initialize agents
        self._initialize_agents()
        
        # Start background processes
        self.monitoring_active = False
    
    def _initialize_agents(self):
        """Initialize all HR agents"""
        self.agents = [
            DocumentDeliveryAgent(self.hr_tools),
            ComplianceMonitoringAgent(self.hr_tools, self.db),
            ProactiveAssistanceAgent(self.hr_tools)
        ]
    
    async def schedule_task(self, task: AutonomousTask) -> str:
        """Schedule an autonomous task"""
        self.task_queue.append(task)
        self.logger.info(f"Scheduled task {task.task_id} of type {task.task_type}")
        return task.task_id
    
    async def execute_immediate_task(self, task: AutonomousTask) -> Dict[str, Any]:
        """Execute a task immediately"""
        suitable_agent = self._find_suitable_agent(task.task_type)
        
        if not suitable_agent:
            return {"success": False, "error": "No suitable agent found"}
        
        task.status = TaskStatus.RUNNING
        self.running_tasks[task.task_id] = task
        
        try:
            result = await suitable_agent.execute(task)
            task.status = TaskStatus.COMPLETED if result.get("success") else TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.result = result
            
            self.completed_tasks.append(task)
            del self.running_tasks[task.task_id]
            
            return result
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
            
            self.completed_tasks.append(task)
            del self.running_tasks[task.task_id]
            
            return {"success": False, "error": str(e)}
    
    def _find_suitable_agent(self, task_type: str) -> Optional[HRAgent]:
        """Find an agent that can handle the task type"""
        for agent in self.agents:
            if agent.can_handle(task_type):
                return agent
        return None
    
    async def start_proactive_monitoring(self):
        """Start proactive monitoring processes"""
        self.monitoring_active = True
        
        # Schedule regular compliance checks
        await self._schedule_compliance_monitoring()
        
        # Schedule proactive assistance
        await self._schedule_proactive_assistance()
    
    async def _schedule_compliance_monitoring(self):
        """Schedule regular compliance monitoring tasks"""
        # Daily attendance compliance check
        daily_task = AutonomousTask(
            task_id=f"compliance_daily_{datetime.now().strftime('%Y%m%d')}",
            task_type="attendance_compliance",
            description="Daily attendance compliance monitoring",
            priority=TaskPriority.MEDIUM,
            scheduled_time=datetime.now() + timedelta(hours=1)
        )
        await self.schedule_task(daily_task)
    
    async def _schedule_proactive_assistance(self):
        """Schedule proactive assistance tasks"""
        # Weekly deadline reminders
        reminder_task = AutonomousTask(
            task_id=f"reminders_{datetime.now().strftime('%Y%m%d')}",
            task_type="remind_deadlines",
            description="Weekly deadline reminders",
            priority=TaskPriority.LOW,
            scheduled_time=datetime.now() + timedelta(days=1)
        )
        await self.schedule_task(reminder_task)
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific task"""
        # Check running tasks
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            return {
                "task_id": task.task_id,
                "status": task.status.value,
                "created_at": task.created_at.isoformat(),
                "description": task.description
            }
        
        # Check completed tasks
        for task in self.completed_tasks:
            if task.task_id == task_id:
                return {
                    "task_id": task.task_id,
                    "status": task.status.value,
                    "created_at": task.created_at.isoformat(),
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "description": task.description,
                    "result": task.result
                }
        
        return None
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        return {
            "agents_active": len(self.agents),
            "tasks_queued": len(self.task_queue),
            "tasks_running": len(self.running_tasks),
            "tasks_completed_today": len([
                t for t in self.completed_tasks 
                if t.completed_at and t.completed_at.date() == datetime.now().date()
            ]),
            "monitoring_active": self.monitoring_active,
            "last_health_check": datetime.now().isoformat()
        }
