"""
Professional HR Output Formatter
Provides consistent, structured, and professional response formatting
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum
from dataclasses import dataclass
import json
import html
import re

class ResponseType(Enum):
    """Types of HR responses"""
    INFORMATION = "information"
    CONFIRMATION = "confirmation"
    VALIDATION_REQUEST = "validation_request"
    ERROR = "error"
    SUCCESS = "success"
    GUIDANCE = "guidance"
    ESCALATION = "escalation"

class OutputFormat(Enum):
    """Output format types"""
    HTML = "html"
    JSON = "json"
    PLAIN_TEXT = "plain_text"
    STRUCTURED = "structured"

@dataclass
class ResponseMetadata:
    """Metadata for responses"""
    response_id: str
    timestamp: datetime
    employee_id: str
    session_id: str
    response_type: ResponseType
    confidence_score: float
    processing_time: float
    tools_used: List[str]
    language: str = "english"

class HROutputFormatter:
    """Professional output formatting system for HR responses"""
    
    def __init__(self):
        self.templates = self._load_templates()
        self.style_guide = self._load_style_guide()
        self.audit_trail = []
    
    def _load_templates(self) -> Dict[str, Dict[str, str]]:
        """Load response templates for different scenarios"""
        return {
            "leave_balance": {
                "html": """
<div class="hr-response leave-balance">
    <div class="response-header">
        <h3 class="title">📅 Your Leave Balance</h3>
        <p class="subtitle">Current status as of {current_date}</p>
    </div>
    <div class="balance-summary">
        <div class="balance-grid">
            <div class="balance-item">
                <span class="label">Sick Leave:</span>
                <span class="value">{sick_leave} days</span>
            </div>
            <div class="balance-item">
                <span class="label">Casual Leave:</span>
                <span class="value">{casual_leave} days</span>
            </div>
            <div class="balance-item">
                <span class="label">Earned Leave:</span>
                <span class="value">{earned_leave} days</span>
            </div>
        </div>
        <div class="total-balance">
            <strong>Total Available: {total_balance} days</strong>
        </div>
    </div>
    <div class="response-footer">
        <p class="help-text">Need help planning your leave? Just ask!</p>
    </div>
</div>
""",
                "structured": {
                    "type": "leave_balance_response",
                    "data": {
                        "employee_id": "{employee_id}",
                        "financial_year": "{financial_year}",
                        "balances": {
                            "sick_leave": "{sick_leave}",
                            "casual_leave": "{casual_leave}",
                            "earned_leave": "{earned_leave}",
                            "total": "{total_balance}"
                        },
                        "last_updated": "{last_updated}"
                    },
                    "metadata": {
                        "response_type": "information",
                        "confidence": 1.0
                    }
                }
            },
            
            "document_request_validation": {
                "html": """
<div class="hr-response validation-request">
    <div class="response-header">
        <h3 class="title">📋 Document Request Validation</h3>
        <p class="subtitle">I need a few details to process your {document_type} request</p>
    </div>
    <div class="validation-form">
        <div class="required-info">
            <h4>Required Information:</h4>
            <ol class="validation-list">
                {validation_items}
            </ol>
        </div>
        <div class="help-section">
            <p class="help-text">Please provide these details and I'll process your request immediately.</p>
        </div>
    </div>
</div>
""",
                "structured": {
                    "type": "validation_request",
                    "data": {
                        "document_type": "{document_type}",
                        "required_fields": "{required_fields}",
                        "validation_rules": "{validation_rules}"
                    },
                    "next_steps": "{next_steps}",
                    "metadata": {
                        "response_type": "validation_request",
                        "requires_user_input": True
                    }
                }
            },
            
            "policy_information": {
                "html": """
<div class="hr-response policy-info">
    <div class="response-header">
        <h3 class="title">📖 {policy_title}</h3>
        <p class="subtitle">Policy Information</p>
    </div>
    <div class="policy-content">
        <div class="policy-summary">
            <h4>Summary:</h4>
            <p>{policy_summary}</p>
        </div>
        <div class="key-points">
            <h4>Key Points:</h4>
            <ul>
                {key_points}
            </ul>
        </div>
        {additional_info}
    </div>
    <div class="response-footer">
        <p class="help-text">Need clarification on any specific aspect? Feel free to ask!</p>
    </div>
</div>
""",
                "structured": {
                    "type": "policy_information",
                    "data": {
                        "policy_area": "{policy_area}",
                        "title": "{policy_title}",
                        "summary": "{policy_summary}",
                        "key_points": "{key_points}",
                        "effective_date": "{effective_date}",
                        "last_updated": "{last_updated}"
                    },
                    "related_resources": "{related_resources}",
                    "metadata": {
                        "response_type": "information",
                        "policy_version": "{policy_version}"
                    }
                }
            },
            
            "success_confirmation": {
                "html": """
<div class="hr-response success">
    <div class="response-header">
        <h3 class="title">✅ {action_title}</h3>
        <p class="subtitle">Request completed successfully</p>
    </div>
    <div class="success-details">
        <div class="confirmation-info">
            <p><strong>What was done:</strong> {action_description}</p>
            <p><strong>Reference ID:</strong> {reference_id}</p>
            <p><strong>Completed at:</strong> {completion_time}</p>
        </div>
        {additional_details}
    </div>
    <div class="response-footer">
        <p class="help-text">Is there anything else I can help you with?</p>
    </div>
</div>
""",
                "structured": {
                    "type": "success_confirmation",
                    "data": {
                        "action": "{action_type}",
                        "reference_id": "{reference_id}",
                        "completion_time": "{completion_time}",
                        "details": "{action_details}"
                    },
                    "metadata": {
                        "response_type": "success",
                        "confidence": 1.0
                    }
                }
            },
            
            "error_response": {
                "html": """
<div class="hr-response error">
    <div class="response-header">
        <h3 class="title">⚠️ Unable to Process Request</h3>
        <p class="subtitle">We encountered an issue</p>
    </div>
    <div class="error-details">
        <p class="error-message">{error_message}</p>
        <div class="suggested-actions">
            <h4>What you can do:</h4>
            <ul>
                {suggested_actions}
            </ul>
        </div>
    </div>
    <div class="response-footer">
        <p class="help-text">If the issue persists, please contact HR directly.</p>
    </div>
</div>
""",
                "structured": {
                    "type": "error_response",
                    "data": {
                        "error_code": "{error_code}",
                        "error_message": "{error_message}",
                        "suggested_actions": "{suggested_actions}"
                    },
                    "metadata": {
                        "response_type": "error",
                        "requires_escalation": "{requires_escalation}"
                    }
                }
            }
        }
    
    def _load_style_guide(self) -> Dict[str, Any]:
        """Load style guide for consistent formatting"""
        return {
            "colors": {
                "primary": "#2c3e50",
                "success": "#27ae60",
                "warning": "#f39c12",
                "error": "#e74c3c",
                "info": "#3498db"
            },
            "typography": {
                "title_size": "1.2em",
                "subtitle_size": "1em",
                "body_size": "0.9em",
                "font_family": "Arial, sans-serif"
            },
            "spacing": {
                "section_margin": "15px",
                "item_padding": "8px",
                "border_radius": "4px"
            },
            "language_styles": {
                "english": {"direction": "ltr", "font_weight": "normal"},
                "hindi": {"direction": "ltr", "font_weight": "normal"},
                "arabic": {"direction": "rtl", "font_weight": "normal"}
            }
        }
    
    def format_response(
        self,
        response_type: str,
        data: Dict[str, Any],
        metadata: ResponseMetadata,
        output_format: OutputFormat = OutputFormat.HTML,
        language: str = "english"
    ) -> Dict[str, Any]:
        """Format a response according to professional standards"""
        
        # Get appropriate template
        template = self.templates.get(response_type, {})
        
        if output_format == OutputFormat.HTML:
            formatted_content = self._format_html_response(template, data, language)
        elif output_format == OutputFormat.STRUCTURED:
            formatted_content = self._format_structured_response(template, data)
        elif output_format == OutputFormat.JSON:
            formatted_content = self._format_json_response(data, metadata)
        else:
            formatted_content = self._format_plain_text_response(data)
        
        # Create complete response
        response = {
            "content": formatted_content,
            "metadata": {
                "response_id": metadata.response_id,
                "timestamp": metadata.timestamp.isoformat(),
                "employee_id": metadata.employee_id,
                "session_id": metadata.session_id,
                "response_type": metadata.response_type.value,
                "confidence_score": metadata.confidence_score,
                "processing_time": metadata.processing_time,
                "tools_used": metadata.tools_used,
                "language": language,
                "format": output_format.value
            },
            "audit_info": {
                "formatted_at": datetime.now().isoformat(),
                "template_used": response_type,
                "data_sanitized": True
            }
        }
        
        # Log for audit trail
        self._log_response(response, metadata)
        
        return response
    
    def _format_html_response(
        self,
        template: Dict[str, Any],
        data: Dict[str, Any],
        language: str
    ) -> str:
        """Format HTML response with proper styling"""
        
        html_template = template.get("html", "<p>{content}</p>")
        
        # Sanitize data
        sanitized_data = self._sanitize_data(data)
        
        # Apply language-specific styling
        style_attrs = self.style_guide["language_styles"].get(language, {})
        
        try:
            # Format the template
            formatted_html = html_template.format(**sanitized_data)
            
            # Add CSS styling
            styled_html = self._add_css_styling(formatted_html, style_attrs)
            
            return styled_html
            
        except KeyError as e:
            # Fallback for missing template variables
            return f"<div class='hr-response error'><p>Response formatting error: {str(e)}</p></div>"
    
    def _format_structured_response(
        self,
        template: Dict[str, Any],
        data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Format structured response"""
        
        structured_template = template.get("structured", {})
        sanitized_data = self._sanitize_data(data)
        
        try:
            # Deep format the structured template
            formatted_structure = self._deep_format_dict(structured_template, sanitized_data)
            return formatted_structure
            
        except Exception as e:
            return {
                "type": "error",
                "message": f"Structured formatting error: {str(e)}",
                "raw_data": sanitized_data
            }
    
    def _format_json_response(
        self,
        data: Dict[str, Any],
        metadata: ResponseMetadata
    ) -> Dict[str, Any]:
        """Format JSON response"""
        return {
            "data": self._sanitize_data(data),
            "metadata": {
                "response_id": metadata.response_id,
                "timestamp": metadata.timestamp.isoformat(),
                "confidence": metadata.confidence_score
            }
        }
    
    def _format_plain_text_response(self, data: Dict[str, Any]) -> str:
        """Format plain text response"""
        sanitized_data = self._sanitize_data(data)
        
        # Convert dict to readable text
        text_parts = []
        for key, value in sanitized_data.items():
            if isinstance(value, (list, dict)):
                text_parts.append(f"{key}: {json.dumps(value, indent=2)}")
            else:
                text_parts.append(f"{key}: {value}")
        
        return "\n".join(text_parts)
    
    def _sanitize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize data for safe output"""
        sanitized = {}
        
        for key, value in data.items():
            if isinstance(value, str):
                # HTML escape and clean
                sanitized[key] = html.escape(value).strip()
            elif isinstance(value, (int, float, bool)):
                sanitized[key] = value
            elif isinstance(value, list):
                sanitized[key] = [self._sanitize_value(item) for item in value]
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_data(value)
            else:
                sanitized[key] = str(value)
        
        return sanitized
    
    def _sanitize_value(self, value: Any) -> Any:
        """Sanitize individual values"""
        if isinstance(value, str):
            return html.escape(value).strip()
        elif isinstance(value, dict):
            return self._sanitize_data(value)
        else:
            return value
    
    def _add_css_styling(self, html_content: str, style_attrs: Dict[str, str]) -> str:
        """Add CSS styling to HTML content"""
        
        css_styles = f"""
        <style>
        .hr-response {{
            font-family: {self.style_guide['typography']['font_family']};
            max-width: 600px;
            margin: {self.style_guide['spacing']['section_margin']} 0;
            border-radius: {self.style_guide['spacing']['border_radius']};
            background: #f8f9fa;
            border-left: 4px solid {self.style_guide['colors']['primary']};
            padding: {self.style_guide['spacing']['item_padding']};
            direction: {style_attrs.get('direction', 'ltr')};
        }}
        .hr-response .title {{
            color: {self.style_guide['colors']['primary']};
            font-size: {self.style_guide['typography']['title_size']};
            margin: 0 0 5px 0;
            font-weight: bold;
        }}
        .hr-response .subtitle {{
            color: #666;
            font-size: {self.style_guide['typography']['subtitle_size']};
            margin: 0 0 10px 0;
        }}
        .hr-response.success {{ border-left-color: {self.style_guide['colors']['success']}; }}
        .hr-response.error {{ border-left-color: {self.style_guide['colors']['error']}; }}
        .hr-response.validation-request {{ border-left-color: {self.style_guide['colors']['info']}; }}
        .balance-grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 10px 0; }}
        .balance-item {{ display: flex; justify-content: space-between; padding: 5px; background: white; border-radius: 3px; }}
        .help-text {{ font-style: italic; color: #666; margin-top: 10px; }}
        </style>
        """
        
        return css_styles + html_content
    
    def _deep_format_dict(self, template: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively format dictionary templates"""
        result = {}
        
        for key, value in template.items():
            if isinstance(value, str) and "{" in value:
                try:
                    result[key] = value.format(**data)
                except KeyError:
                    result[key] = value  # Keep original if formatting fails
            elif isinstance(value, dict):
                result[key] = self._deep_format_dict(value, data)
            elif isinstance(value, list):
                result[key] = [
                    item.format(**data) if isinstance(item, str) and "{" in item else item
                    for item in value
                ]
            else:
                result[key] = value
        
        return result
    
    def _log_response(self, response: Dict[str, Any], metadata: ResponseMetadata):
        """Log response for audit trail"""
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "response_id": metadata.response_id,
            "employee_id": metadata.employee_id,
            "session_id": metadata.session_id,
            "response_type": metadata.response_type.value,
            "tools_used": metadata.tools_used,
            "processing_time": metadata.processing_time,
            "content_length": len(str(response.get("content", ""))),
            "language": metadata.language
        }
        
        self.audit_trail.append(audit_entry)
        
        # Keep only last 1000 entries
        if len(self.audit_trail) > 1000:
            self.audit_trail = self.audit_trail[-1000:]
    
    def get_audit_trail(self, employee_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get audit trail, optionally filtered by employee"""
        if employee_id:
            return [entry for entry in self.audit_trail if entry["employee_id"] == employee_id]
        return self.audit_trail.copy()
