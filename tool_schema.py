TOOLS_SCHEMA = [
    {
        "type": "function",
        "function": {
            "name": "get_hr_context_tool",
            "description": "Get HR policies, processes, guidelines, holidays, and benefits information. Use ONLY for general HR policy questions, NOT personal data.",
            "parameters": {
                "type": "object",
                "properties": {
                    "question": {
                        "type": "string",
                        "description": "The specific HR policy or procedure question"
                    }
                },
                "required": ["question"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_leave_balance_tool",
            "description": "Get employee's personal leave balance information. Use ONLY when user specifically asks about their leave balance.",
            "parameters": {
                "type": "object",
                "properties": {
                    "emp_code": {
                        "type": "string",
                        "description": "Employee code (optional, uses logged in employee if not provided)"
                    }
                },
                "required": []
            }
        }
    },
     {
        "type": "function",
        "function": {
            "name": "get_mrf_tool",
            "description": "Get employee's MRF (Manpower Requisition Form) details and status information. Use when user asks about MRF requests, hiring requests, or manpower requisition status.",
            "parameters": {
                "type": "object",
                "properties": {
                    "emp_code": {
                        "type": "string",
                        "description": "Employee code (optional, uses logged in employee if not provided)"
                    }
                },
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_payout_tool",
            "description": "Get employee's payout information including variable pays, yearly bonus, retention bonus, incentives, and all types of bonus payments. Use when user asks about bonuses, variable pay, incentives, or payout details.",
            "parameters": {
                "type": "object",
                "properties": {
                    "emp_code": {
                        "type": "string",
                        "description": "Employee code (optional, uses logged in employee if not provided)"
                    }
                },
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_medical_insurance_tool",
            "description": "Get employee's personal medical insurance coverage details, policy numbers, covered family members. Use ONLY for personal insurance coverage info, NOT claim procedures.",
            "parameters": {
                "type": "object",
                "properties": {
                    "emp_code": {
                        "type": "string",
                        "description": "Employee code (optional, uses logged in employee if not provided)"
                    }
                },
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_buddy_ref_tool",
            "description": "Get employee referral information, buddy system data, referral bonus status. Use ONLY for referral/buddy system queries.",
            "parameters": {
                "type": "object",
                "properties": {
                    "emp_code": {
                        "type": "string",
                        "description": "Employee code (optional, uses logged in employee if not provided)"
                    }
                },
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_attendance_tool",
            "description": "Get employee's personal attendance records, present/absent days, working hours, PIPO details, LOP details. Use ONLY for personal attendance data queries.",
            "parameters": {
                "type": "object",
                "properties": {
                    "emp_code": {
                        "type": "string",
                        "description": "Employee code (optional, uses logged in employee if not provided)"
                    },
                    "months": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of months in YYYY-MM format"
                    }
                    # "period_text": {
                    #     "type": "string",
                    #     "description": "Natural language period like 'last month', 'current month', 'last 3 months'"
                    # }
                },
                "required": ["months"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "send_payslip_tool",
            "description": "Send payslips via email. Use ONLY when user explicitly requests to SEND/EMAIL payslips. Requires complete information: months and email preference.",
            "parameters": {
                "type": "object",
                "properties": {
                    "emp_code": {
                        "type": "string",
                        "description": "Employee code (optional, uses logged in employee if not provided)"
                    },
                    "months": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of months in YYYY-MM format((required))"
                    },
                    "email_type": {
                        "type": "string",
                        "enum": ["personal_email", "official_email", "both_emails"],
                        "description": "Where to send the payslip,Email preference: 'personal' or 'official' (required)"
                    }
                },
                "required": ["months", "email_type"]
            }
        }
    },
    {
    "type": "function",
    "function": {
        "name": "send_form16_tool",
        "description": " Send Form 16  (form types:Part A, Part B, Tax Computation) via email. Use ONLY when user explicitly requests to SEND/EMAIL Form 16. Requires complete information: financial year, form type, and email preference",
        "parameters": {
            "type": "object",
            "properties": {
                "emp_code": {
                    "type": "string",
                    "description": "Employee code (optional, uses logged-in employee if not provided)"
                },
                "years": {
                    "type": "array",
                    "items": { "type": "string" },
                    "description": "List of financial years in YYYY-YYYY format (e.g. 2022-2023, 2023-2024)(required)"
                },
                "form_types": {
                    "type": "array",
                    "items": { 
                        "type": "string",
                        "enum": ["part_a", "part_b", "tax_comp"]
                    },
                    "description": "Form type: 'Part A', 'Part B', or 'Tax Computation' (required)"
                },
                "email_type": {
                    "type": "string",
                    "enum": ["personal_email", "official_email", "both_emails"],
                        "description": "Email preference: 'personal' or 'official' (required)"
                }
            },
            "required": ["years", "form_types", "email_type"]
        }
    }},
{
    "type": "function",
    "function": {
        "name": "get_reimbursement_tool",
        "description": "Get employee's reimbursement information. Can fetch general eligibility/options (no months needed) or specific month-wise claim status (months required)",
        "parameters": {
            "type": "object",
            "properties": {
                "emp_code": {
                    "type": "string",
                    "description": "Employee code (optional, uses logged-in employee if not provided)"
                },
                "months": {
                    "type": "array",
                    "items": { "type": "string" },
                    "description": "List of months in YYYY-MM format for which reimbursement info is required. Optional - if not provided, returns general eligibility and opted reimbursements"                },
                "period_text": {
                    "type": "string",
                    "description": "Natural language period like 'last month', 'current month', 'last 3 months'. Only used when specific month data is needed"
                }
            },
            "required": []
        }
    }
}




    
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "send_form16_otp_tool",
    #         "description": "Send OTP for Form 16 request",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "emp_code": {
    #                     "type": "string",
    #                     "description": "Employee code (optional, uses logged in employee if not provided)"
    #                 },
    #                 "years": {
    #                     "type": "array",
    #                     "items": {"type": "string"},
    #                     "description": "List of financial years in YYYY-YY format"
    #                 }
    #             },
    #             "required": ["years"]
    #         }
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "validate_and_send_form16_tool",
    #         "description": "Validate OTP and send Form 16",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "emp_code": {
    #                     "type": "string",
    #                     "description": "Employee code (optional, uses logged in employee if not provided)"
    #                 },
    #                 "years": {
    #                     "type": "array",
    #                     "items": {"type": "string"},
    #                     "description": "List of financial years in YYYY-YY format"
    #                 },
    #                 "otp": {
    #                     "type": "string",
    #                     "description": "OTP received by user"
    #                 },
    #                 "email_type": {
    #                     "type": "string",
    #                     "enum": ["personal_email", "official_email", "both_emails"],
    #                     "description": "Where to send the Form 16"
    #                 }
    #             },
    #             "required": ["years", "otp", "email_type"]
    #         }
    #     }
    # }
]