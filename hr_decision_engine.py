"""
Enhanced HR Decision Engine
Provides intelligent decision-making capabilities for complex HR scenarios
"""

from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import re
from abc import ABC, abstractmethod

class HRScenarioType(Enum):
    """Types of HR scenarios the bot can handle"""
    LEAVE_REQUEST = "leave_request"
    PAYROLL_INQUIRY = "payroll_inquiry"
    POLICY_QUESTION = "policy_question"
    DOCUMENT_REQUEST = "document_request"
    ATTENDANCE_ISSUE = "attendance_issue"
    BENEFITS_INQUIRY = "benefits_inquiry"
    DISCIPLINARY_MATTER = "disciplinary_matter"
    ONBOARDING = "onboarding"
    PERFORMANCE_REVIEW = "performance_review"
    GRIEVANCE = "grievance"

class DecisionConfidence(Enum):
    """Confidence levels for decisions"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    REQUIRES_HUMAN = "requires_human"

@dataclass
class DecisionContext:
    """Context information for making HR decisions"""
    employee_id: str
    employee_data: Dict[str, Any]
    scenario_type: HRScenarioType
    user_query: str
    conversation_history: List[Dict[str, str]]
    current_date: datetime
    additional_context: Dict[str, Any] = None

@dataclass
class HRDecision:
    """Result of an HR decision"""
    action: str
    confidence: DecisionConfidence
    reasoning: str
    required_tools: List[str]
    validation_steps: List[str]
    escalation_needed: bool
    follow_up_actions: List[str]
    response_template: str

class HRDecisionRule(ABC):
    """Abstract base class for HR decision rules"""
    
    @abstractmethod
    def applies_to(self, context: DecisionContext) -> bool:
        """Check if this rule applies to the given context"""
        pass
    
    @abstractmethod
    def evaluate(self, context: DecisionContext) -> HRDecision:
        """Evaluate the context and return a decision"""
        pass

class LeaveRequestRule(HRDecisionRule):
    """Rules for handling leave requests"""
    
    def applies_to(self, context: DecisionContext) -> bool:
        leave_keywords = ['leave', 'vacation', 'time off', 'sick leave', 'casual leave', 'earned leave']
        query_lower = context.user_query.lower()
        return any(keyword in query_lower for keyword in leave_keywords)
    
    def evaluate(self, context: DecisionContext) -> HRDecision:
        query_lower = context.user_query.lower()
        
        # Check if it's a balance inquiry
        if any(word in query_lower for word in ['balance', 'remaining', 'available', 'left']):
            return HRDecision(
                action="get_leave_balance",
                confidence=DecisionConfidence.HIGH,
                reasoning="User is asking about leave balance",
                required_tools=["get_leave_balance_tool"],
                validation_steps=["verify_employee_access"],
                escalation_needed=False,
                follow_up_actions=["offer_leave_application_help"],
                response_template="leave_balance_response"
            )
        
        # Check if it's a leave application
        elif any(word in query_lower for word in ['apply', 'request', 'take', 'need']):
            return HRDecision(
                action="guide_leave_application",
                confidence=DecisionConfidence.MEDIUM,
                reasoning="User wants to apply for leave",
                required_tools=["get_hr_context_tool", "get_leave_balance_tool"],
                validation_steps=["check_leave_policy", "verify_balance"],
                escalation_needed=False,
                follow_up_actions=["provide_application_process"],
                response_template="leave_application_guidance"
            )
        
        return HRDecision(
            action="general_leave_info",
            confidence=DecisionConfidence.MEDIUM,
            reasoning="General leave-related query",
            required_tools=["get_hr_context_tool"],
            validation_steps=[],
            escalation_needed=False,
            follow_up_actions=["ask_for_clarification"],
            response_template="general_leave_response"
        )

class DocumentRequestRule(HRDecisionRule):
    """Rules for handling document requests"""
    
    def applies_to(self, context: DecisionContext) -> bool:
        doc_keywords = ['payslip', 'form16', 'form 16', 'salary slip', 'tax certificate', 'download', 'send']
        query_lower = context.user_query.lower()
        return any(keyword in query_lower for keyword in doc_keywords)
    
    def evaluate(self, context: DecisionContext) -> HRDecision:
        query_lower = context.user_query.lower()
        
        # Payslip request
        if any(word in query_lower for word in ['payslip', 'salary slip']):
            return HRDecision(
                action="process_payslip_request",
                confidence=DecisionConfidence.HIGH,
                reasoning="User is requesting payslip",
                required_tools=["send_payslip_tool"],
                validation_steps=["validate_date_range", "validate_email_preference"],
                escalation_needed=False,
                follow_up_actions=["confirm_delivery"],
                response_template="payslip_request_validation"
            )
        
        # Form16 request
        elif any(word in query_lower for word in ['form16', 'form 16', 'tax certificate']):
            return HRDecision(
                action="process_form16_request",
                confidence=DecisionConfidence.HIGH,
                reasoning="User is requesting Form16",
                required_tools=["send_form16_tool"],
                validation_steps=["validate_financial_year", "validate_form_type", "validate_email_preference"],
                escalation_needed=False,
                follow_up_actions=["confirm_delivery"],
                response_template="form16_request_validation"
            )
        
        return HRDecision(
            action="clarify_document_request",
            confidence=DecisionConfidence.LOW,
            reasoning="Unclear document request",
            required_tools=[],
            validation_steps=[],
            escalation_needed=False,
            follow_up_actions=["ask_for_specific_document"],
            response_template="document_clarification"
        )

class PolicyInquiryRule(HRDecisionRule):
    """Rules for handling policy inquiries"""
    
    def applies_to(self, context: DecisionContext) -> bool:
        policy_keywords = ['policy', 'rule', 'procedure', 'process', 'how to', 'what is', 'guideline']
        query_lower = context.user_query.lower()
        return any(keyword in query_lower for keyword in policy_keywords)
    
    def evaluate(self, context: DecisionContext) -> HRDecision:
        return HRDecision(
            action="provide_policy_information",
            confidence=DecisionConfidence.HIGH,
            reasoning="User is asking about HR policies or procedures",
            required_tools=["get_hr_context_tool"],
            validation_steps=[],
            escalation_needed=False,
            follow_up_actions=["offer_related_information"],
            response_template="policy_information_response"
        )

class HRDecisionEngine:
    """Main decision engine for HR scenarios"""
    
    def __init__(self):
        self.rules = [
            LeaveRequestRule(),
            DocumentRequestRule(),
            PolicyInquiryRule(),
        ]
        self.decision_history = []
    
    def analyze_scenario(self, context: DecisionContext) -> HRScenarioType:
        """Analyze the context to determine the scenario type"""
        query_lower = context.user_query.lower()
        
        # Keyword-based scenario detection
        scenario_keywords = {
            HRScenarioType.LEAVE_REQUEST: ['leave', 'vacation', 'time off', 'sick', 'casual'],
            HRScenarioType.PAYROLL_INQUIRY: ['salary', 'pay', 'payroll', 'bonus', 'increment'],
            HRScenarioType.DOCUMENT_REQUEST: ['payslip', 'form16', 'certificate', 'download', 'send'],
            HRScenarioType.POLICY_QUESTION: ['policy', 'rule', 'procedure', 'process', 'guideline'],
            HRScenarioType.ATTENDANCE_ISSUE: ['attendance', 'punch', 'late', 'absent', 'working hours'],
            HRScenarioType.BENEFITS_INQUIRY: ['insurance', 'medical', 'benefit', 'reimbursement'],
        }
        
        for scenario_type, keywords in scenario_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return scenario_type
        
        return HRScenarioType.POLICY_QUESTION  # Default fallback
    
    def make_decision(self, context: DecisionContext) -> HRDecision:
        """Make an intelligent decision based on the context"""
        
        # Find applicable rules
        applicable_rules = [rule for rule in self.rules if rule.applies_to(context)]
        
        if not applicable_rules:
            # Fallback decision
            return HRDecision(
                action="general_hr_assistance",
                confidence=DecisionConfidence.LOW,
                reasoning="No specific rule matched the query",
                required_tools=["get_hr_context_tool"],
                validation_steps=[],
                escalation_needed=True,
                follow_up_actions=["escalate_to_human"],
                response_template="general_assistance"
            )
        
        # Use the first applicable rule (can be enhanced with priority system)
        decision = applicable_rules[0].evaluate(context)
        
        # Log the decision
        self.decision_history.append({
            "timestamp": datetime.now(),
            "context": context,
            "decision": decision
        })
        
        return decision
    
    def get_multi_step_reasoning(self, context: DecisionContext) -> List[str]:
        """Provide multi-step reasoning for complex decisions"""
        reasoning_steps = []
        
        # Step 1: Analyze user intent
        reasoning_steps.append(f"Analyzing user query: '{context.user_query}'")
        
        # Step 2: Determine scenario type
        scenario = self.analyze_scenario(context)
        reasoning_steps.append(f"Identified scenario type: {scenario.value}")
        
        # Step 3: Check employee context
        if context.employee_data:
            reasoning_steps.append(f"Employee context available for {context.employee_data.get('empname', 'employee')}")
        
        # Step 4: Apply business rules
        reasoning_steps.append("Applying relevant HR business rules")
        
        # Step 5: Determine required actions
        decision = self.make_decision(context)
        reasoning_steps.append(f"Recommended action: {decision.action}")
        
        return reasoning_steps

# Decision tree templates for common workflows
DECISION_TREES = {
    "leave_application": {
        "root": "check_leave_type",
        "nodes": {
            "check_leave_type": {
                "question": "What type of leave do you need?",
                "options": {
                    "sick": "check_sick_leave_balance",
                    "casual": "check_casual_leave_balance",
                    "earned": "check_earned_leave_balance"
                }
            },
            "check_sick_leave_balance": {
                "action": "get_leave_balance",
                "next": "validate_sick_leave_request"
            }
        }
    }
}
