from packages import *

# Import your existing modules
from tools_reg.cross_2 import *
from tools_reg.leave_test import *
from tools_reg.medical_test import *
from tools_reg.payslip import *
from tools_reg.buddy_ref import *
from tools_reg.attendance_test import *
from tools_reg.form16 import *
from tools_reg.reimbursement import *


import config

def set_emp_id(emp_id: int):
    print(f"EMP_ID updated to: {config.EMP_ID}")

    config.EMP_ID = emp_id
    print(f"EMP_ID updated to: {config.EMP_ID}")


import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# EMP_ID = None

from tools import *

# === Tool Definitions for Function Calling ===
from tool_schema import *
from packages import *
from validate_emp import *

# === Database Connection ===
def connect_to_mongo(host, port, user_name, pass_word, db_name):
    if "social" in user_name:
        return MongoClient("***************************************************************************************************")['jdsocial']
    return MongoClient(f'mongodb://{user_name}:{urllib.parse.quote_plus(pass_word)}@{host}:{port}')[db_name]

# Initialize database connection
try:
    db = connect_to_mongo("**************", 27017, "jdsocial", "jdsocial123", "jdsocial")
    chat_logs_collection = db['hr_chat_logs']
    chat_sessions_collection = db['hr_chat_sessions']
    logger.info("Database connection established successfully")
except Exception as e:
    logger.error(f"Database connection failed: {e}")
    db = None
    chat_logs_collection = None
    chat_sessions_collection = None

# Create a mapping of function names to actual functions
TOOL_FUNCTIONS = {
    "get_hr_context_tool": get_hr_context_tool,
    "get_leave_balance_tool": get_leave_balance_tool,
    "get_medical_insurance_tool": get_medical_insurance_tool,
    "get_buddy_ref_tool": get_buddy_ref_tool,
    "get_attendance_tool": get_attendance_tool,
    "get_reimbursement_tool": get_reimbursement_tool,
    "send_payslip_tool": send_payslip_tool,
    "send_form16_tool":send_form16_tool,
    # "send_form16_otp_tool": send_form16_otp_tool,
    # "validate_and_send_form16_tool": validate_and_send_form16_tool,
}

def generate_chat_heading(question: str, emp_context: dict) -> str:
    """Generate a descriptive heading for the chat session based on the first question."""
    try:
        # Use LLM to generate a concise heading
        llm = QwenChatLLM()
        
        heading_prompt = f"""
        Generate a short, descriptive heading (3-6 words) for this HR conversation:
        
        Employee Question: "{question}"
        Employee: {emp_context.get('empname', 'Employee')} - {emp_context.get('designation', '')}
        
        Examples of good headings:
        - "Leave Balance Inquiry"
        - "Payslip Request October"
        - "Medical Insurance Query"
        - "Form 16 Download"
        - "Attendance Issues"
        
        Return only the heading, nothing else.
        """
        
        response = llm([HumanMessage(content=heading_prompt)])
        heading = response.content.strip().strip('"').strip("'")
        
        # Fallback if LLM fails
        if not heading or len(heading) > 50:
            if "leave" in question.lower():
                heading = "Leave Related Query"
            elif "payslip" in question.lower():
                heading = "Payslip Request"
            elif "form 16" in question.lower() or "form16" in question.lower():
                heading = "Form 16 Request"
            elif "attendance" in question.lower():
                heading = "Attendance Query"
            elif "medical" in question.lower() or "insurance" in question.lower():
                heading = "Medical Insurance Query"
            elif "reimbursement" in question.lower():
                heading = "Reimbursement Query"
            else:
                heading = "HR Assistance"
        
        return heading
    except Exception as e:
        logger.error(f"Error generating chat heading: {e}")
        return "HR Query"

def check_session_exists(session_id: str) -> dict:
    """Check if session exists in database."""
    if chat_sessions_collection is None:
        return {"exists": False, "session_data": None}
    
    try:
        session_data = chat_sessions_collection.find_one({"session_id": session_id})
        return {"exists": bool(session_data), "session_data": session_data}
    except Exception as e:
        logger.error(f"Error checking session: {e}")
        return {"exists": False, "session_data": None}

def create_new_session(session_id: str, emp_code: str, heading: str, user_ip: str) -> bool:
    """Create a new chat session."""
    if chat_sessions_collection is None:
        return False
    
    try:
        session_doc = {
            "session_id": session_id,
            "emp_code": emp_code,
            "heading": heading,
            "created_at": datetime.now(),
            "user_ip": user_ip,
            "last_activity": datetime.now()
        }
        
        chat_sessions_collection.insert_one(session_doc)
        logger.info(f"New session created: {session_id}")
        return True
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        return False

def log_chat_interaction(session_id: str, emp_code: str, heading: str, user_question: str, 
                        bot_response: str, response_time: float, status_code: int, user_ip: str):
    """Log each chat interaction."""
    if chat_logs_collection is None:
        return
    
    try:
        log_doc = {
            "session_id": session_id,
            "emp_code": emp_code,
            "heading": heading,
            "user_question": user_question,
            "bot_response": bot_response,
            "response_time_seconds": response_time,
            "status_code": status_code,
            "user_ip": user_ip,
            "timestamp": datetime.now(),
            "created_at": datetime.now()
        }
        
        chat_logs_collection.insert_one(log_doc)
        
        # Update session last activity
        if chat_sessions_collection is not None:
            chat_sessions_collection.update_one(
                {"session_id": session_id},
                {"$set": {"last_activity": datetime.now()}}
            )
        
        logger.info(f"Chat interaction logged for session: {session_id}")
    except Exception as e:
        logger.error(f"Error logging chat interaction: {e}")


# === Enhanced QwenChatLLM with Function Calling ===
class QwenChatLLM:
    def __init__(self, model: str = "qwen_r", base_url: str = "http://*************:5005/v1"):
        self.model = model
        self.base_url = base_url
        self.max_retries = 3
        self.retry_delay = 1.0

    def __call__(self, messages: List, tools: List = None, **kwargs):
        """Make a call to the LLM with optional function calling."""
        openai_msgs = []
        for m in messages:
            if hasattr(m, 'content'):
                content = m.content
            else:
                content = str(m)
                
            if isinstance(m, HumanMessage):
                openai_msgs.append({"role": "user", "content": content})
            elif isinstance(m, AIMessage):
                openai_msgs.append({"role": "assistant", "content": content})
            else:
                openai_msgs.append({"role": "user", "content": content})

        payload = {
            "model": self.model,
            "messages": openai_msgs,
            "temperature": 0,
        }
        
        # Add tools if provided
        if tools:
            payload["tools"] = tools
            payload["tool_choice"] = "auto"

        try:
            resp = requests.post(f"{self.base_url}/chat/completions", json=payload, timeout=60)
            resp.raise_for_status()
            result = resp.json()
            
            print("******************************")
            print("LLM Response:", json.dumps(result, indent=2)[:1000] + "..." if len(str(result)) > 1000 else result)
            print("******************************")
            
            choice = result['choices'][0]
            message = choice['message']
            
            # Handle function calls
            if message.get('tool_calls'):
                return self._create_function_call_response(message)
            else:
                content = message['content']
                if content and "</think>" in content:
                    content = content.split("</think>")[-1].strip()
                return MockAIMessage(content=content)
                
        except Exception as e:
            print(f"LLM call failed: {e}")
            return MockAIMessage(content="I'm experiencing technical difficulties. Please try again.")

    def _create_function_call_response(self, message):
        """Create a response object for function calls."""
        return MockAIMessageWithTools(
            content=message.get('content', ''),
            tool_calls=message['tool_calls']
        )

# Mock classes to handle responses
class MockAIMessage:
    def __init__(self, content: str):
        self.content = content
        self.tool_calls = None

class MockAIMessageWithTools:
    def __init__(self, content: str, tool_calls: List):
        self.content = content
        self.tool_calls = tool_calls

# === Agent State ===
class AgentState(TypedDict):
    messages: Annotated[List[AnyMessage], operator.add]
    employee_context: dict
    payslip_state: dict

# === Enhanced call_model function ===
def call_model(state: AgentState):
    now = datetime.now()
    ist_tz = pytz.timezone('Asia/Kolkata')
    now_ist = now.astimezone(ist_tz) if now.tzinfo else ist_tz.localize(now)
    
    emp_context = state.get("employee_context", {})
    
    # Check if we have tool results to process
    tool_msgs = [m for m in state["messages"] if isinstance(m, ToolMessage)]
    
    if tool_msgs:
        # Process tool results - same as your existing logic
        tool_results_text = []
        for tm in tool_msgs:
            label = tm.name.replace("_tool", "").replace("get_", "").replace("_", " ").title()
            content = tm.content
            tool_results_text.append(f"{label}:\n{content}")
        
        tool_results_block = "\n\n".join(tool_results_text)
        
        user_msg = None
        for m in reversed(state["messages"]):
            if isinstance(m, HumanMessage):
                user_msg = m
                break

        final_system = f"""
You are JIA-Justdial Intelligent Assistant. Using the tool results below, provide a natural, conversational response.

Employee Context:
- Name: {emp_context.get("empname", "")} (ID: {emp_context.get("empcode", "")})
- Designation: {emp_context.get("designation", "")} | Department: {emp_context.get("department", "")}

Tool Results:
{tool_results_block}

Provide a direct, helpful answer based solely on the tool results. Keep it under 100 words and be conversational.
"""

        messages = [HumanMessage(content=final_system)]
        if user_msg:
            messages.append(user_msg)

        llm = QwenChatLLM()
        ai_msg_content = llm(messages).content
        return {"messages": [AIMessage(content=ai_msg_content)]}

    # Initial processing with function calling
    context_info = f"""
Employee Context:
- Name: {emp_context.get("empname", "")} (Employee ID: {emp_context.get("empcode", "")})
- Designation: {emp_context.get("designation", "")}
- Department: {emp_context.get("department", "")}
- Location: {emp_context.get("city", "")}
- Email: {emp_context.get("email_id", "")}
- Reporting Manager: {emp_context.get("reporting_head", "")}
"""


    system_prompt = f"""
You are JIA-Justdial Intelligent Assistant, a friendly HR assistant.

📅 Current Date: {now.strftime('%B %d, %Y at %H:%M IST')}

{emp_context}

## 🚨 CRITICAL SECURITY GUARDRAILS - ENFORCE STRICTLY

### TOPIC BOUNDARIES - HR ONLY
- ONLY respond to HR-related queries: leave, attendance, policies, benefits, payroll, insurance, reimbursements
- IMMEDIATELY redirect non-HR topics with: "I'm your HR assistant and can only help with employee-related matters. What can I assist you with regarding your HR needs?"
- NO exceptions for: math problems, coding questions, general knowledge, personal advice, technical help

### ZERO TECHNICAL DISCLOSURE
- NEVER mention tool names (get_leave_balance_tool, send_payslip_tool, etc.) to users
- NEVER share API URLs, endpoints, system details, or technical implementations
- NEVER provide code examples, scripts, or programming help in any language
- Simply say: "Let me check that information for you" instead of exposing internal processes

### STANDARD RESPONSES FOR NON-HR QUERIES
**Math/Calculations:** "I'm here for HR support only. Need help with leave balance, salary, or policies?"
**Code/Programming:** "I can't help with technical questions. I'm designed for HR assistance. What employee information do you need?"
**General Knowledge:** "I focus on HR matters only. How can I help with your employee needs today?"
**System Details:** "I can't share technical information. Let me help you with HR queries instead."


You have access to various HR tools. When users ask questions that require data retrieval, use the appropriate function calls:

- For For HR policies, processes, guidelines, holidays, benefits, insurance procedures, claim processes, who to contact, how-to guides (automatically includes employee context): use get_hr_context_tool
- For personal leave balance information : use get_leave_balance_tool  
- For employee's personal medical insurance coverage details, policy numbers, family members covered - NOT for claim processes: use get_medical_insurance_tool
- For attendance records, present/absent days, working hours, LOP (Loss of Pay) details,PIPO(PunchIN /Punch out) details,leaves (PIPO, sick/casual) and any all data related to atttendance: use get_attendance_tool
- For Employee referral information, buddy system data, referral bonus status, and referral history: use get_buddy_ref_tool
- For employee's reimbursement claims status, fuel allowance, conveyance, mobile/internet, education, leave travel allowance (LTA), and any reimbursement-related information: use get_reimbursement_tool

- **Payslips**: use `send_payslip_tool` - For sending payslips (requires validation)
- **Form 16**: use `send_form16_tool` - For sending Form 16 documents (requires validation)

## 🚨 CRITICAL RULES FOR PAYSLIP & FORM 16 OPERATIONS

### Pre-Tool Call Requirements
**NEVER call tools without complete information. Always gather ALL required details first through conversation.**

### Payslip Rules (`send_payslip_tool`)
**Required Information:**
1. **Months** (specific month-year combinations)
2. **Email preference** (personal/official)

**Month Validation Logic:**
- **Current running month**: Payslip not available - reject politely
- **"Last 3 months"**: Calculate dynamically (exclude current running month)
- **"Last month"**: Previous completed month only
- **Future months**: Always reject any future requests
- **Specific months**: Must be completed months only (not current/future)

**Validation Questions (ask crisp and short):**
- "Which specific months do you need?"
- "Should I send to your personal or official email?"

### Form 16 Rules (`send_form16_tool`)
**Required Information:**
1. **Financial Year** (in YYYY-YY format, e.g., 2023-24)
2. **Form Type** (Part A, Part B, or Tax Computation)
3. **Email preference** (personal/official)

**Year Validation Logic:**
- **"Last 2 years"**: Calculate dynamically (previous 2 completed financial years)
- **"Last year"**: Previous completed financial year only
- **Current financial year**: Reject - "Form 16 for current year is not available yet"
- **Future years**: Always reject any future financial year requests
- **Available years**: Only completed financial years (calculate dynamically)

**Validation Questions (ask crisp and short):**
- "Which financial year? (e.g., 2024-25, 2023-24)"
- "Which form type: Part A, Part B, or Tax Computation?"
- "Should I send to your personal or official email?"

### Error Handling
- **Incomplete information**: Ask for missing details conversationally
- **Invalid months/years**: Politely explain why not available
- **Future requests**: "That period is not available yet"
- **No hallucination**: Never assume or invent months/years/details

### Conversation Flow Example
User: "Send me last 3 months payslip"
JIA: "I'll send payslips for the last 3 completed months. Should I send them to your personal or official email?"
User: "Send Form 16 for last year"
JIA: "I'll send Form 16 for the previous financial year. Which type do you need: Part A, Part B, or Tax Computation? And should I send to your personal or official email?"

## General Behavior
- Be warm, professional, and conversational
- Use employee context naturally
- Ask crisp, short questions when gathering information
- Only proceed with tool calls when ALL required information is collected
- No assumptions or hallucinations about dates, months, or years

Be warm, professional, and conversational. Use employee context naturally.

"""


    """
    - For Sending payslip for specified months to given email type: use send_payslip_tool (requires month validation and email preference)
    - For sending Form 16 (Part A, Part B, Tax Computation) for specified financial years to given email type: use send_form16_tool (requires year validation, form type selection, and email preference)

    For secure operations like payslips and Form 16:
    1. Always validate required information (months/years, form types, email preference)
    2. Ask for missing information conversationally
    3. Only proceed when all requirements are met



    Be warm, professional, and conversational. Use employee context naturally.
    """

    # Get recent conversation for context
    recent_messages = state["messages"][-10:] if len(state["messages"]) > 10 else state["messages"]
    
    conversation_msgs = [HumanMessage(content=system_prompt)]
    for msg in recent_messages:
        if isinstance(msg, (HumanMessage, AIMessage)):
            conversation_msgs.append(msg)

    try:
        llm = QwenChatLLM()
        response = llm(conversation_msgs, tools=TOOLS_SCHEMA)
        
        # Check if response contains function calls
        if hasattr(response, 'tool_calls') and response.tool_calls:
            # Return the response with tool calls - the router will handle them
            return {"messages": [AIMessage(content=response.content or "", 
                                          additional_kwargs={"tool_calls": response.tool_calls})]}
        else:
            return {"messages": [AIMessage(content=response.content)]}
            
    except Exception as e:
        logger.error(f"LLM call failed: {e}")
        fallback_response = "I'm experiencing technical difficulties. Please try again or contact HR directly."
        return {"messages": [AIMessage(content=fallback_response)]}

# === Enhanced tool router ===
def tool_router(state: AgentState):
    last_msg = state["messages"][-1]
    emp_context = state.get("employee_context", {})
    tool_messages = []
    
    # Check for function calls in additional_kwargs
    if hasattr(last_msg, 'additional_kwargs') and 'tool_calls' in last_msg.additional_kwargs:
        tool_calls = last_msg.additional_kwargs['tool_calls']
        
        for tool_call in tool_calls:
            function_name = tool_call['function']['name']
            function_args = json.loads(tool_call['function']['arguments'])
            tool_call_id = tool_call['id']
            
            print(f"Executing function: {function_name} with args: {function_args}")
            
            if function_name in TOOL_FUNCTIONS:
                try:
                    # Special handling for hr_context tool to pass emp_context
                    if function_name == "get_hr_context_tool":
                        function_args['emp_context'] = emp_context
                    
                    # Execute the function
                    result = TOOL_FUNCTIONS[function_name](**function_args)
                    
                    tool_messages.append(
                        ToolMessage(
                            name=function_name,
                            content=str(result),
                            tool_call_id=tool_call_id
                        )
                    )
                    
                except Exception as e:
                    error_msg = f"Error executing {function_name}: {str(e)}"
                    print(error_msg)
                    tool_messages.append(
                        ToolMessage(
                            name=function_name,
                            content=error_msg,
                            tool_call_id=tool_call_id
                        )
                    )
    
    # Fallback: Check for old-style TOOL_CALL patterns in content
    elif hasattr(last_msg, 'content') and "TOOL_CALL" in last_msg.content:
        # Keep your existing regex patterns as fallback
        patterns = [
            ("get_leave_balance_tool", r"TOOL_CALL\s*:\s*get_leave_balance\((\d{4,})?\)", 
             lambda empcode: get_leave_balance_tool(empcode), "leave_{}"),
            ("get_hr_context_tool", r"TOOL_CALL\s*:\s*get_hr_context\(([^)]+)\)", 
             lambda question: get_hr_context_tool(question.strip().strip('"\''), emp_context), "hr_{}"),
            ("get_medical_insurance_tool", r"TOOL_CALL\s*:\s*get_medical_insurance\((\d{4,})?\)", 
             lambda empcode: get_medical_insurance_tool(empcode), "med_{}"),
            ("get_buddy_ref_tool", r"TOOL_CALL\s*:\s*get_buddy_ref\((\d{4,})?\)", 
             lambda empcode: get_buddy_ref_tool(empcode), "bud_{}"),
        ]
        
        for tool_name, pattern, func, call_id_fmt in patterns:
            matches = re.findall(pattern, last_msg.content)
            for param in matches:
                try:
                    result = func(param if param else None)
                    tool_messages.append(
                        ToolMessage(
                            name=tool_name, 
                            content=str(result), 
                            tool_call_id=call_id_fmt.format(param or "default")
                        )
                    )
                except Exception as e:
                    tool_messages.append(
                        ToolMessage(
                            name=tool_name,
                            content=f"Error: {str(e)}",
                            tool_call_id=call_id_fmt.format(param or "error")
                        )
                    )
    
    print(f"Tool messages generated: {[tm.name for tm in tool_messages]}")
    return {"messages": tool_messages} if tool_messages else {"messages": []}

def should_continue(state: AgentState) -> Literal["tools", "__end__"]:
    last_message = state["messages"][-1]
    
    # Check for function calls
    if hasattr(last_message, 'additional_kwargs') and 'tool_calls' in last_message.additional_kwargs:
        return "tools"
    
    # Check for old-style tool calls
    if hasattr(last_message, 'content'):
        if "TOOL_CALL" in last_message.content:
            return "tools"
        elif "NO_TOOL" in last_message.content:
            last_message.content = "I do not have this information. Please connect with the HR team."
            return "__end__"
    
    return "__end__"

# === Build the graph ===
graph = StateGraph(AgentState)
graph.add_node("agent", call_model)
graph.add_node("tools", tool_router)
graph.set_entry_point("agent")
graph.add_conditional_edges("agent", should_continue, {"tools": "tools", "__end__": END})
graph.add_edge('tools', 'agent')
app = graph.compile()

# === Employee Validation (unchanged) ===

# === FastAPI Implementation (unchanged structure) ===

fastapi_app = FastAPI()

class Message(BaseModel):
    role: str
    content: str

class QueryRequest(BaseModel):
    emp_id: str
    question: str
    session_id:Optional[str] = None  # New field for session management

    
    history: List[Message] = []


def get_client_ip(request: Request) -> str:
    """Extract client IP address from request."""
    x_forwarded_for = request.headers.get('x-forwarded-for')
    if x_forwarded_for:
        return x_forwarded_for.split(',')[0].strip()
    return request.client.host if request.client else "unknown"


@fastapi_app.post("/ask_hr")
def ask_question(request: QueryRequest,http_request: Request):
    start_time = time.time()
    user_ip = get_client_ip(http_request)


    # global EMP_ID

    emp_id = request.emp_id.strip() if request.emp_id else ""
    history = [{"role": h.role, "content": h.content} for h in request.history]
    question = request.question.strip()
    session_id = request.session_id
    
    # Generate session_id if not provided
    if not session_id:
        session_id = str(uuid.uuid4())



    if not emp_id:
        response_time = time.time() - start_time
        error_response = {
            "emp_id": None,
            "question": question,
            "session_id": session_id,
            "answer": "Hi there! I'd be happy to help you with your HR questions. Could you please provide your employee ID so I can assist you better?",
            "error": 1,
            "error_msg": "Employee ID is required"
        }
        
        # Log even error interactions
        log_chat_interaction(
            session_id=session_id,
            emp_code="",
            heading="Authentication Required",
            user_question=question,
            bot_response=error_response["answer"],
            response_time=response_time,
            status_code=400,
            user_ip=user_ip
        )
        
        return error_response


    validation = validate_employee(emp_id)
    if not validation["valid"]:
        response_time = time.time() - start_time
        error_response = {
            "emp_id": emp_id,
            "question": question,
            "session_id": session_id,
            "answer": "I'm having trouble finding your employee record. Could you please double-check your employee ID or contact IT support?",
            "error": 1,
            "error_msg": f"Employee validation failed: {validation['error']}"
        }
        
        # Log validation error
        log_chat_interaction(
            session_id=session_id,
            emp_code=emp_id,
            heading="Validation Error",
            user_question=question,
            bot_response=error_response["answer"],
            response_time=response_time,
            status_code=401,
            user_ip=user_ip
        )
        
        return error_response

    # Set global EMP_ID after successful validation
    # EMP_ID = emp_id
    set_emp_id(emp_id)

    
    print(f"Global EMP_ID set to: {config.EMP_ID}")

    try:
        
          # Check if session exists
        session_check = check_session_exists(session_id)
        
        # If session doesn't exist, create new session with heading
        if not session_check["exists"]:
            heading = generate_chat_heading(question, validation.get("data", {}))
            create_new_session(session_id, emp_id, heading, user_ip)
        else:
            heading = session_check["session_data"]["heading"]

        # Convert history to LangChain message objects
        messages = []
        for msg in history[-10:]:  # Keep last 10 messages for context
            if msg["role"] == "user":
                messages.append(HumanMessage(content=msg["content"]))
            elif msg["role"] == "assistant":
                messages.append(AIMessage(content=msg["content"]))

        # Add the new user question
        current_question = f"{question} (empcode: {emp_id})"
        messages.append(HumanMessage(content=current_question))

        # Create initial state with employee context
        initial_state = {
            "messages": messages,
            "employee_context": validation.get("data", {}),
            "payslip_state": {}
        }

        # Stream the conversation
        answer = ""
        for event in app.stream(initial_state, stream_mode="values"):
            last_message = event["messages"][-1]
            if hasattr(last_message, "content"):
                answer = last_message.content
                # Clean up any thinking tags
                if "</think>" in answer:
                    answer = answer.split("</think>")[-1].strip()

        # Get employee name for response context
        emp_name = validation["data"].get("empname") if validation["data"] else None
        
        if emp_name and answer:
            # Keep the answer as is, just ensure it's clean
            answer = answer.strip()
            
        response_time = time.time() - start_time
          # Log successful interaction
        log_chat_interaction(
            session_id=session_id,
            emp_code=emp_id,
            heading=heading,
            user_question=question,
            bot_response=answer,
            response_time=response_time,
            status_code=200,
            user_ip=user_ip
        )

        return {
            "emp_id": emp_id,
            "question": question,
            "answer": answer,
            "error": 0
        }

    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        response_time = time.time() - start_time
        
        error_response = {
            "emp_id": emp_id,
            "question": question,
            "session_id": session_id,
            "answer": "I apologize, but I'm experiencing some technical difficulties right now. Please try again in a moment or contact HR directly if it's urgent.",
            "error": 1,
            "error_msg": str(e)
        }
        
        # Log system error
        log_chat_interaction(
            session_id=session_id,
            emp_code=emp_id,
            heading="System Error",
            user_question=question,
            bot_response=error_response["answer"],
            response_time=response_time,
            status_code=500,
            user_ip=user_ip
        )
        
        return error_response
    
    
# === Health check endpoint ===
@fastapi_app.get("/health")
def health_check():
    """Health check endpoint to verify API and database connectivity."""
    return {
        "status": "healthy",
        "database_connected":db is not None,
        "timestamp": datetime.now().isoformat()
    }

@fastapi_app.get("/sessions/employee/{emp_code}")
def get_employee_sessions(emp_code: str, limit: int = 20):
    """Get recent sessions for an employee."""
    if chat_sessions_collection is None:
        return {"error": "Database not available", "sessions": []}
    
    try:
        sessions = list(chat_sessions_collection.find(
            {"emp_code": emp_code}
        ).sort("last_activity", -1).limit(limit))
        
        # Convert ObjectId to string for JSON serialization
        for session in sessions:
            session["_id"] = str(session["_id"])
        
        return {"sessions": sessions}
    except Exception as e:
        logger.error(f"Error fetching employee sessions: {e}")
        return {"error": str(e), "sessions": []}
# === Session management endpoints ===
@fastapi_app.get("/session/{session_id}")
def get_session_info(session_id: str):
    """Get session information."""
    session_check = check_session_exists(session_id)
    if session_check["exists"]:
        session_data = session_check["session_data"]
        return {
            "session_id": session_id,
            "exists": True,
            "emp_code": session_data.get("emp_code"),
            "heading": session_data.get("heading"),
            "created_at": session_data.get("created_at"),
            "last_activity": session_data.get("last_activity")
        }
    else:
        return {
            "session_id": session_id,
            "exists": False
        }

@fastapi_app.get("/chat-logs/session/{session_id}")
def get_session_chat_logs(session_id: str, limit: int = 50):
    """Get chat logs for a specific session formatted for frontend display."""
    if chat_logs_collection is None:
        return {"error": "Database not available", "data": []}
    
    try:
        # Fetch logs from database
        logs = list(chat_logs_collection.find(
            {"session_id": session_id}
        ).sort("timestamp", 1).limit(limit))
        
        # Transform logs into chat message format
        chat_messages = []
        for log in logs:
            # Add user message user assistant
            chat_messages.append({
                "role": "user",
                "message": log["user_question"],
                "timestamp": log["timestamp"].isoformat() if isinstance(log["timestamp"], datetime) else log["timestamp"]
            })
            
            # Add assistant message
            chat_messages.append({
                "role": "assistant", 
                "message": log["bot_response"],
                "timestamp": log["timestamp"].isoformat() if isinstance(log["timestamp"], datetime) else log["timestamp"]
            })
        
        return {
            "error":0,
            "session_id": session_id,
            "data": chat_messages,
            "total_messages": len(chat_messages),
            "total_interactions": len(logs)
        }
        
    except Exception as e:
        logger.error(f"Error fetching session chat logs: {e}")
        return {"error": 1, "error_message":str(e), "data": []}

@fastapi_app.get("/chat-logs/session/{session_id}/raw")
def get_session_chat_logs(session_id: str, limit: int = 50):
    """Get chat logs for a specific session."""
    if chat_logs_collection is None:
        return {"error": "Database not available", "logs": []}
    
    try:
        logs = list(chat_logs_collection.find(
            {"session_id": session_id}
        ).sort("timestamp", 1).limit(limit))
        
        # Convert ObjectId to string for JSON serialization
        for log in logs:
            log["_id"] = str(log["_id"])
        
        return {"logs": logs}
    except Exception as e:
        logger.error(f"Error fetching session chat logs: {e}")
        return {"error": str(e), "logs": []}

@fastapi_app.get("/analytics/daily-stats")
def get_daily_stats(date_str: str = None):
    """Get daily analytics stats."""
    if chat_logs_collection is None:
        return {"error": "Database not available"}
    
    try:
        if date_str:
            target_date = datetime.strptime(date_str, "%Y-%m-%d")
        else:
            target_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        next_date = target_date + timedelta(days=1)
        
        pipeline = [
            {
                "$match": {
                    "timestamp": {
                        "$gte": target_date,
                        "$lt": next_date
                    }
                }
            },
            {
                "$group": {
                    "_id": None,
                    "total_interactions": {"$sum": 1},
                    "unique_employees": {"$addToSet": "$emp_code"},
                    "unique_sessions": {"$addToSet": "$session_id"},
                    "avg_response_time": {"$avg": "$response_time_seconds"},
                    "max_response_time": {"$max": "$response_time_seconds"},
                    "min_response_time": {"$min": "$response_time_seconds"},
                    "successful_interactions": {
                        "$sum": {"$cond": [{"$eq": ["$status_code", 200]}, 1, 0]}
                    },
                    "error_interactions": {
                        "$sum": {"$cond": [{"$ne": ["$status_code", 200]}, 1, 0]}
                    }
                }
            }
        ]
        
        result = list(chat_logs_collection.aggregate(pipeline))
        
        if result:
            stats = result[0]
            stats["unique_employees_count"] = len(stats["unique_employees"])
            stats["unique_sessions_count"] = len(stats["unique_sessions"])
            del stats["unique_employees"]
            del stats["unique_sessions"]
            del stats["_id"]
        else:
            stats = {
                "total_interactions": 0,
                "unique_employees_count": 0,
                "unique_sessions_count": 0,
                "avg_response_time": 0,
                "successful_interactions": 0,
                "error_interactions": 0
            }
        
        return {
            "date": target_date.strftime("%Y-%m-%d"),
            "stats": stats
        }
    except Exception as e:
        logger.error(f"Error fetching daily stats: {e}")
        return {"error": str(e)}

    


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(fastapi_app, host="0.0.0.0", port=5011)