# Enhanced Date Validation System for HR Bot

import datetime
import pytz
from typing import List, Dict, Union, Optional
import re
from dateutil.relativedelta import relativedelta

class DateValidationError(Exception):
    """Custom exception for date validation errors"""
    pass

class HRDateValidator:
    """Comprehensive date validation for HR operations"""
    
    def __init__(self):
        self.ist_tz = pytz.timezone('Asia/Kolkata')
        self.current_date = self.get_current_ist_date()
        
    def get_current_ist_date(self) -> datetime.datetime:
        """Get current date in IST timezone"""
        now = datetime.datetime.now()
        if now.tzinfo:
            return now.astimezone(self.ist_tz)
        else:
            return self.ist_tz.localize(now)
    
    def parse_month_input(self, month_input: str) -> List[str]:
        """
        Parse various month input formats and return standardized YYYY-MM list
        
        Examples:
        - "sep 2025" -> ["2025-09"]
        - "last 3 months" -> ["2025-06", "2025-07", "2025-08"] (if current is 2025-09)
        - "last month" -> ["2025-08"]
        - "2025-08" -> ["2025-08"]
        """
        month_input = month_input.strip().lower()
        
        # Handle relative terms
        if "last month" in month_input:
            return [self.get_last_completed_month()]
        
        if "last" in month_input and "months" in month_input:
            # Extract number from "last X months"
            match = re.search(r'last\s+(\d+)\s+months?', month_input)
            if match:
                count = int(match.group(1))
                return self.get_last_n_completed_months(count)
        
        if "current month" in month_input or "this month" in month_input:
            raise DateValidationError("Current month payslip is not available yet. Only completed months are available.")
        
        # Handle specific month formats
        return self.parse_specific_month_formats(month_input)
    
    def parse_specific_month_formats(self, month_input: str) -> List[str]:
        """Parse specific month formats like 'sep 2025', '2025-09', 'september 2025'"""
        
        # Pattern for YYYY-MM format
        if re.match(r'\d{4}-\d{2}', month_input):
            return [month_input]
        
        # Pattern for "MMM YYYY" or "Month YYYY"
        month_patterns = {
            'jan': '01', 'january': '01',
            'feb': '02', 'february': '02',
            'mar': '03', 'march': '03',
            'apr': '04', 'april': '04',
            'may': '05',
            'jun': '06', 'june': '06',
            'jul': '07', 'july': '07',
            'aug': '08', 'august': '08',
            'sep': '09', 'september': '09',
            'oct': '10', 'october': '10',
            'nov': '11', 'november': '11',
            'dec': '12', 'december': '12'
        }
        
        # Try to match month name/abbreviation with year
        for month_name, month_num in month_patterns.items():
            if month_name in month_input:
                # Extract year
                year_match = re.search(r'\b(20\d{2})\b', month_input)
                if year_match:
                    year = year_match.group(1)
                    return [f"{year}-{month_num}"]
        
        raise DateValidationError(f"Unable to parse month format: '{month_input}'. Please use formats like 'Sep 2025', '2025-09', or 'last month'")
    
    def get_last_completed_month(self) -> str:
        """Get the last completed month in YYYY-MM format"""
        last_month = self.current_date.replace(day=1) - relativedelta(months=1)
        return last_month.strftime("%Y-%m")
    
    def get_last_n_completed_months(self, n: int) -> List[str]:
        """Get last N completed months in YYYY-MM format"""
        months = []
        current_month = self.current_date.replace(day=1)
        
        for i in range(1, n + 1):
            month = current_month - relativedelta(months=i)
            months.append(month.strftime("%Y-%m"))
        
        return list(reversed(months))  # Return in chronological order
    
    def validate_months_for_payslip(self, months: List[str]) -> Dict[str, Union[List[str], List[str]]]:
        """
        Validate months for payslip requests
        Returns: {
            "valid_months": [...],
            "invalid_months": [...]
        }
        """
        valid_months = []
        invalid_months = []
        
        current_month_str = self.current_date.strftime("%Y-%m")
        
        for month in months:
            try:
                # Parse month
                month_date = datetime.datetime.strptime(month + "-01", "%Y-%m-%d")
                month_str = month_date.strftime("%Y-%m")
                
                # Check if future month
                if month_str > current_month_str:
                    invalid_months.append({
                        "month": month,
                        "reason": f"Future month ({month}) - payslip not available yet"
                    })
                # Check if current running month
                elif month_str == current_month_str:
                    invalid_months.append({
                        "month": month,
                        "reason": f"Current month ({month}) - payslip not available yet"
                    })
                else:
                    valid_months.append(month)
                    
            except ValueError:
                invalid_months.append({
                    "month": month,
                    "reason": f"Invalid month format: {month}"
                })
        
        return {
            "valid_months": valid_months,
            "invalid_months": invalid_months
        }
    
    def parse_financial_year_input(self, fy_input: str) -> List[str]:
        """
        Parse financial year input formats
        
        Examples:
        - "last year" -> ["2023-24"] (if current FY is 2024-25)
        - "last 2 years" -> ["2022-23", "2023-24"]
        - "2023-24" -> ["2023-24"]
        """
        fy_input = fy_input.strip().lower()
        
        if "last year" in fy_input and "years" not in fy_input:
            return [self.get_last_completed_financial_year()]
        
        if "last" in fy_input and "years" in fy_input:
            match = re.search(r'last\s+(\d+)\s+years?', fy_input)
            if match:
                count = int(match.group(1))
                return self.get_last_n_completed_financial_years(count)
        
        if "current year" in fy_input or "this year" in fy_input:
            raise DateValidationError("Current financial year Form 16 is not available yet.")
        
        # Direct FY format like "2023-24"
        if re.match(r'\d{4}-\d{2}', fy_input):
            return [fy_input]
        
        raise DateValidationError(f"Unable to parse financial year format: '{fy_input}'. Please use formats like '2023-24' or 'last year'")
    
    def get_current_financial_year(self) -> str:
        """Get current financial year in YYYY-YY format"""
        current_date = self.current_date
        if current_date.month >= 4:  # April onwards
            start_year = current_date.year
        else:  # Jan-Mar
            start_year = current_date.year - 1
        
        end_year = start_year + 1
        return f"{start_year}-{str(end_year)[2:]}"
    
    def get_last_completed_financial_year(self) -> str:
        """Get last completed financial year"""
        current_fy = self.get_current_financial_year()
        start_year = int(current_fy.split('-')[0]) - 1
        end_year = start_year + 1
        return f"{start_year}-{str(end_year)[2:]}"
    
    def get_last_n_completed_financial_years(self, n: int) -> List[str]:
        """Get last N completed financial years"""
        years = []
        current_fy = self.get_current_financial_year()
        current_start_year = int(current_fy.split('-')[0])
        
        for i in range(1, n + 1):
            start_year = current_start_year - i
            end_year = start_year + 1
            years.append(f"{start_year}-{str(end_year)[2:]}")
        
        return list(reversed(years))  # Return in chronological order
    
    def validate_financial_years_for_form16(self, years: List[str]) -> Dict[str, Union[List[str], List[str]]]:
        """Validate financial years for Form 16 requests"""
        valid_years = []
        invalid_years = []
        
        current_fy = self.get_current_financial_year()
        
        for year in years:
            try:
                # Validate format
                if not re.match(r'\d{4}-\d{2}', year):
                    invalid_years.append({
                        "year": year,
                        "reason": f"Invalid format. Use YYYY-YY format (e.g., 2023-24)"
                    })
                    continue
                
                # Check if future year
                if year > current_fy:
                    invalid_years.append({
                        "year": year,
                        "reason": f"Future financial year ({year}) - not available yet"
                    })
                # Check if current year
                elif year == current_fy:
                    invalid_years.append({
                        "year": year,
                        "reason": f"Current financial year ({year}) - Form 16 not available yet"
                    })
                else:
                    valid_years.append(year)
                    
            except Exception:
                invalid_years.append({
                    "year": year,
                    "reason": f"Invalid financial year format: {year}"
                })
        
        return {
            "valid_years": valid_years,
            "invalid_years": invalid_years
        }

# Usage example and integration into the existing system
def enhanced_payslip_validation_handler(user_input: str, emp_code: str = None) -> Dict:
    """
    Enhanced handler for payslip requests with comprehensive validation
    """
    validator = HRDateValidator()
    
    try:
        # Parse the user input to extract months
        months = validator.parse_month_input(user_input)
        
        # Validate the months
        validation_result = validator.validate_months_for_payslip(months)
        
        if validation_result["invalid_months"]:
            # Create user-friendly error message
            error_messages = []
            for invalid in validation_result["invalid_months"]:
                error_messages.append(invalid["reason"])
            
            return {
                "status": "validation_failed",
                "message": "Cannot process payslip request:\n" + "\n".join(error_messages),
                "valid_months": validation_result["valid_months"],
                "requires_user_action": True
            }
        
        if not validation_result["valid_months"]:
            return {
                "status": "no_valid_months",
                "message": "No valid months found for payslip generation.",
                "requires_user_action": True
            }
        
        return {
            "status": "validation_passed",
            "valid_months": validation_result["valid_months"],
            "message": f"Validation successful for months: {', '.join(validation_result['valid_months'])}"
        }
        
    except DateValidationError as e:
        return {
            "status": "parsing_error",
            "message": str(e),
            "requires_user_action": True
        }
    except Exception as e:
        return {
            "status": "system_error",
            "message": "An error occurred while processing your request. Please try again.",
            "requires_user_action": True
        }

def enhanced_form16_validation_handler(user_input: str, emp_code: str = None) -> Dict:
    """
    Enhanced handler for Form 16 requests with comprehensive validation
    """
    validator = HRDateValidator()
    
    try:
        # Parse the user input to extract financial years
        years = validator.parse_financial_year_input(user_input)
        
        # Validate the financial years
        validation_result = validator.validate_financial_years_for_form16(years)
        
        if validation_result["invalid_years"]:
            # Create user-friendly error message
            error_messages = []
            for invalid in validation_result["invalid_years"]:
                error_messages.append(invalid["reason"])
            
            return {
                "status": "validation_failed",
                "message": "Cannot process Form 16 request:\n" + "\n".join(error_messages),
                "valid_years": validation_result["valid_years"],
                "requires_user_action": True
            }
        
        if not validation_result["valid_years"]:
            return {
                "status": "no_valid_years",
                "message": "No valid financial years found for Form 16 generation.",
                "requires_user_action": True
            }
        
        return {
            "status": "validation_passed",
            "valid_years": validation_result["valid_years"],
            "message": f"Validation successful for financial years: {', '.join(validation_result['valid_years'])}"
        }
        
    except DateValidationError as e:
        return {
            "status": "parsing_error",
            "message": str(e),
            "requires_user_action": True
        }
    except Exception as e:
        return {
            "status": "system_error",
            "message": "An error occurred while processing your request. Please try again.",
            "requires_user_action": True
        }