accelerate==1.9.0
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.14
aiosignal==1.4.0
altair==5.5.0
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
asttokens==3.0.0
async-timeout==5.0.1
attrs==25.3.0
backcall==0.2.0
backoff==2.2.1
beautifulsoup4==4.13.4
bleach==6.2.0
blinker==1.9.0
cachetools==5.5.2
certifi==2025.7.14
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
coloredlogs==15.0.1
contourpy==1.3.2
cryptography==45.0.5
cycler==0.12.1
dataclasses-json==0.6.7
decorator==5.2.1
defusedxml==0.7.1
Deprecated==1.2.18
distro==1.9.0
dnspython==2.7.0
docopt==0.6.2
duckduckgo_search==8.1.1
effdet==0.4.1
emoji==2.14.1
executing==2.2.0
fastapi==0.116.1
fastjsonschema==2.21.1
filelock==3.18.0
filetype==1.2.0
flatbuffers==25.2.10
fonttools==4.59.0
frozenlist==1.7.0
fsspec==2025.7.0
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.6.15
google-api-core==2.25.1
google-api-python-client==2.178.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-cloud-vision==3.10.2
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
greenlet==3.2.3
griffe==1.11.0
groq==0.30.0
grpcio==1.73.1
grpcio-status==1.71.2
h11==0.16.0
h2==4.2.0
hf-xet==1.1.5
hpack==4.1.0
html5lib==1.1
httpcore==1.0.9
httplib2==0.22.0
httpx==0.28.1
httpx-sse==0.4.1
huggingface-hub==0.33.4
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
iniconfig==2.1.0
ipython==8.12.3
jedi==0.19.2
Jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
jupyter_client==8.6.3
jupyter_core==5.8.1
jupyterlab_pygments==0.3.0
kiwisolver==1.4.8
langchain==0.3.26
langchain-community==0.3.27
langchain-core==0.3.69
langchain-google-genai==2.1.9
langchain-groq==0.3.6
langchain-text-splitters==0.3.8
langdetect==1.0.9
langgraph==0.5.3
langgraph-checkpoint==2.1.0
langgraph-prebuilt==0.5.2
langgraph-sdk==0.1.73
langsmith==0.4.6
lxml==6.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
matplotlib-inline==0.1.7
mcp==1.12.4
mistune==3.1.3
mpmath==1.3.0
multidict==6.6.3
mypy_extensions==1.1.0
narwhals==1.48.0
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.5
nltk==3.9.1
numpy==2.2.6
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==11.3.0.4
nvidia-cufile-cu12==1.11.1.6
nvidia-curand-cu12==10.3.7.77
nvidia-cusolver-cu12==11.7.1.2
nvidia-cusparse-cu12==12.5.4.2
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
olefile==0.47
omegaconf==2.3.0
onnx==1.18.0
onnxruntime==1.22.1
openai==1.99.3
openai-agents==0.2.5
opencv-python==*********
orjson==3.11.0
ormsgpack==1.10.0
packaging==24.2
pandas==2.3.1
pandocfilters==1.5.1
parso==0.8.4
pdf2image==1.17.0
pdfminer.six==20250506
pdfplumber==0.11.7
pexpect==4.9.0
pi_heif==1.0.0
pickleshare==0.7.5
pikepdf==9.10.2
pillow==11.3.0
pinecone==7.3.0
pinecone-plugin-assistant==1.7.0
pinecone-plugin-interface==0.0.7
pip-chill==1.0.3
pipreqs==0.5.0
platformdirs==4.3.8
pluggy==1.6.0
portalocker==2.10.1
primp==0.15.0
prompt_toolkit==3.0.51
propcache==0.3.2
proto-plus==1.26.1
protobuf==5.29.5
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==21.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycocotools==2.0.10
pycparser==2.22
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
pydeck==0.9.1
Pygments==2.19.2
pymongo==4.14.1
PyMuPDF==1.26.3
pyparsing==3.2.3
pypdf==5.8.0
pypdfium2==4.30.1
pytest==8.4.1
python-dateutil==2.9.0.post0
python-docx==1.2.0
python-dotenv==1.1.1
python-iso639==2025.2.18
python-magic==0.4.27
python-multipart==0.0.20
python-oxmsg==0.0.2
pytz==2025.2
PyYAML==6.0.2
pyzmq==27.0.0
qdrant-client==1.14.3
RapidFuzz==3.13.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
requests-toolbelt==1.0.0
rpds-py==0.26.0
rsa==4.9.1
safetensors==0.5.3
scikit-learn==1.7.0
scipy==1.16.0
sentence-transformers==5.0.0
setuptools==80.9.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
sse-starlette==3.0.2
stack-data==0.6.3
starlette==0.47.1
streamlit==1.47.0
sympy==1.14.0
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.11.0
timm==1.0.17
tinycss2==1.4.0
tokenizers==0.21.2
toml==0.10.2
torch==2.7.1
torchvision==0.22.1
tornado==6.5.1
tqdm==4.67.1
traitlets==5.14.3
transformers==4.53.2
triton==3.3.1
types-requests==2.32.4.20250611
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
unstructured==0.18.9
unstructured-client==0.39.1
unstructured-inference==1.0.5
unstructured.pytesseract==0.3.15
uritemplate==4.2.0
urllib3==2.5.0
uvicorn==0.35.0
watchdog==6.0.0
wcwidth==0.2.13
webencodings==0.5.1
wrapt==1.17.2
xxhash==3.5.0
yarg==0.1.9
yarl==1.20.1
zstandard==0.23.0
