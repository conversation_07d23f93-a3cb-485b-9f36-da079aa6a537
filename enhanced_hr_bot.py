"""
Enhanced HR Bot - Main Integration Module
Orchestrates all enhanced HR capabilities into a comprehensive intelligent assistant
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid
import logging
from dataclasses import dataclass

# Import enhanced components
from hr_decision_engine import HRDecisionEngine, DecisionContext, HRScenarioType
from hr_prompt_engine import <PERSON><PERSON>romptEngine, PromptType
from hr_user_experience import HRUserExperience, UserContext, UserRole, ConversationState
from hr_data_models import EmployeeProfile, HRRequest, ConversationLog
from hr_agentic_system import HRAgenticOrchestrator, AutonomousTask, TaskPriority
from hr_output_formatter import HROutputFormatter, ResponseType, OutputFormat, ResponseMetadata

# Import existing components
from packages import *
from tools import *
from tool_schema import TOOLS_SCHEMA
import config

class EnhancedHRBot:
    """
    Enhanced HR Bot that integrates all advanced capabilities:
    - Intelligent decision making
    - Advanced prompt engineering
    - Personalized user experience
    - Autonomous task execution
    - Professional output formatting
    """
    
    def __init__(self, db_connection=None):
        self.logger = logging.getLogger(__name__)
        
        # Initialize enhanced components
        self.decision_engine = HRDecisionEngine()
        self.prompt_engine = HRPromptEngine()
        self.user_experience = HRUserExperience()
        self.output_formatter = HROutputFormatter()
        
        # Initialize agentic system
        self.agentic_orchestrator = HRAgenticOrchestrator(
            hr_tools=self._get_hr_tools(),
            db_connection=db_connection
        )
        
        # Initialize existing LLM
        self.llm = QwenChatLLM()
        
        # Session management
        self.active_sessions = {}
        
        self.logger.info("Enhanced HR Bot initialized successfully")
    
    def _get_hr_tools(self) -> Dict[str, Any]:
        """Get HR tools for agentic system"""
        return {
            'get_hr_context_tool': get_hr_context_tool,
            'get_leave_balance_tool': get_leave_balance_tool,
            'get_medical_insurance_tool': get_medical_insurance_tool,
            'get_buddy_ref_tool': get_buddy_ref_tool,
            'get_attendance_tool': get_attendance_tool,
            'get_reimbursement_tool': get_reimbursement_tool,
            'send_payslip_tool': send_payslip_tool,
            'send_form16_tool': send_form16_tool,
            'get_payout_tool': get_payout_tool,
            'get_mrf_tool': get_mrf_tool
        }
    
    async def process_user_query(
        self,
        user_query: str,
        employee_data: Dict[str, Any],
        session_id: str,
        conversation_history: List[Dict[str, str]] = None,
        user_ip: str = None
    ) -> Dict[str, Any]:
        """
        Process user query with enhanced intelligence
        """
        start_time = datetime.now()
        response_id = str(uuid.uuid4())
        
        try:
            # Step 1: Create user context
            user_context = self._create_user_context(
                employee_data, session_id, conversation_history or []
            )
            
            # Step 2: Analyze query and create decision context
            decision_context = DecisionContext(
                employee_id=employee_data.get('empcode', ''),
                employee_data=employee_data,
                scenario_type=self.decision_engine.analyze_scenario(
                    DecisionContext(
                        employee_id=employee_data.get('empcode', ''),
                        employee_data=employee_data,
                        scenario_type=HRScenarioType.POLICY_QUESTION,
                        user_query=user_query,
                        conversation_history=conversation_history or [],
                        current_date=datetime.now()
                    )
                ),
                user_query=user_query,
                conversation_history=conversation_history or [],
                current_date=datetime.now()
            )
            
            # Step 3: Make intelligent decision
            hr_decision = self.decision_engine.make_decision(decision_context)
            
            # Step 4: Create conversation context
            conversation_context = self.user_experience.create_conversation_context(
                user_query, user_context
            )
            
            # Step 5: Generate enhanced system prompt
            prompt_context = {
                'employee_context': employee_data,
                'scenario_type': hr_decision.action,
                'conversation_flow': conversation_context['conversation_flow'],
                'permissions': conversation_context['permissions']
            }
            
            system_prompt = self.prompt_engine.generate_system_prompt(prompt_context)
            
            # Step 6: Execute required tools based on decision
            tool_results = await self._execute_tools(hr_decision, decision_context)
            
            # Step 7: Generate response using LLM
            llm_response = await self._generate_llm_response(
                user_query, system_prompt, tool_results, employee_data
            )
            
            # Step 8: Format professional output
            processing_time = (datetime.now() - start_time).total_seconds()
            
            response_metadata = ResponseMetadata(
                response_id=response_id,
                timestamp=datetime.now(),
                employee_id=employee_data.get('empcode', ''),
                session_id=session_id,
                response_type=self._determine_response_type(hr_decision.action),
                confidence_score=hr_decision.confidence.value if hasattr(hr_decision.confidence, 'value') else 0.8,
                processing_time=processing_time,
                tools_used=hr_decision.required_tools,
                language=self._detect_language(user_query)
            )
            
            formatted_response = self.output_formatter.format_response(
                response_type=hr_decision.response_template,
                data=self._prepare_response_data(llm_response, tool_results, hr_decision),
                metadata=response_metadata,
                output_format=OutputFormat.HTML,
                language=response_metadata.language
            )
            
            # Step 9: Log conversation
            await self._log_conversation(
                session_id, employee_data.get('empcode', ''), user_query,
                formatted_response['content'], processing_time, user_ip
            )
            
            # Step 10: Check for autonomous tasks
            await self._check_autonomous_tasks(decision_context, hr_decision)
            
            return {
                'success': True,
                'response': formatted_response['content'],
                'metadata': formatted_response['metadata'],
                'decision_info': {
                    'action': hr_decision.action,
                    'confidence': hr_decision.confidence.value if hasattr(hr_decision.confidence, 'value') else 'high',
                    'reasoning': hr_decision.reasoning
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error processing query: {str(e)}")
            
            # Generate error response
            error_metadata = ResponseMetadata(
                response_id=response_id,
                timestamp=datetime.now(),
                employee_id=employee_data.get('empcode', ''),
                session_id=session_id,
                response_type=ResponseType.ERROR,
                confidence_score=0.0,
                processing_time=(datetime.now() - start_time).total_seconds(),
                tools_used=[],
                language='english'
            )
            
            error_response = self.output_formatter.format_response(
                response_type='error_response',
                data={
                    'error_message': 'I apologize, but I encountered a technical issue. Please try again or contact HR directly.',
                    'error_code': 'PROCESSING_ERROR',
                    'suggested_actions': ['Try rephrasing your question', 'Contact HR team directly', 'Try again in a few minutes']
                },
                metadata=error_metadata
            )
            
            return {
                'success': False,
                'response': error_response['content'],
                'error': str(e)
            }
    
    def _create_user_context(
        self,
        employee_data: Dict[str, Any],
        session_id: str,
        conversation_history: List[Dict[str, str]]
    ) -> UserContext:
        """Create comprehensive user context"""
        
        user_role = self.user_experience.determine_user_role(employee_data)
        
        return UserContext(
            employee_id=employee_data.get('empcode', ''),
            role=user_role,
            department=employee_data.get('department', ''),
            location=employee_data.get('city', ''),
            manager_id=employee_data.get('reporting_head', ''),
            access_level=self._determine_access_level(user_role),
            preferences={'language': 'english'},
            interaction_history=conversation_history,
            current_session={'employee_data': employee_data, 'session_id': session_id}
        )
    
    def _determine_access_level(self, user_role: UserRole) -> int:
        """Determine access level based on user role"""
        access_levels = {
            UserRole.EMPLOYEE: 1,
            UserRole.TEAM_LEAD: 2,
            UserRole.MANAGER: 3,
            UserRole.HR_REPRESENTATIVE: 4,
            UserRole.HR_ADMIN: 5
        }
        return access_levels.get(user_role, 1)
    
    async def _execute_tools(
        self,
        hr_decision,
        decision_context: DecisionContext
    ) -> Dict[str, Any]:
        """Execute required tools based on HR decision"""
        
        tool_results = {}
        hr_tools = self._get_hr_tools()
        
        for tool_name in hr_decision.required_tools:
            try:
                if tool_name in hr_tools:
                    tool_func = hr_tools[tool_name]
                    
                    # Prepare tool parameters based on decision context
                    if tool_name == 'get_hr_context_tool':
                        result = tool_func(
                            question=decision_context.user_query,
                            emp_context=decision_context.employee_data
                        )
                    else:
                        result = tool_func(emp_code=decision_context.employee_id)
                    
                    tool_results[tool_name] = result
                    
            except Exception as e:
                self.logger.error(f"Error executing tool {tool_name}: {str(e)}")
                tool_results[tool_name] = f"Error: {str(e)}"
        
        return tool_results
    
    async def _generate_llm_response(
        self,
        user_query: str,
        system_prompt: str,
        tool_results: Dict[str, Any],
        employee_data: Dict[str, Any]
    ) -> str:
        """Generate response using LLM with enhanced prompts"""
        
        # Prepare messages
        messages = []
        
        # Add tool results if available
        if tool_results:
            tool_context = "\n\n".join([
                f"{tool_name.replace('_tool', '').replace('get_', '').title()}:\n{result}"
                for tool_name, result in tool_results.items()
            ])
            
            user_prompt = f"""
Employee Request: "{user_query}"

Available HR Data:
{tool_context}

Please provide a professional response based on the data above.
"""
        else:
            user_prompt = f"""
Employee Request: "{user_query}"

Please provide appropriate HR assistance.
"""
        
        messages.append(HumanMessage(content=user_prompt))
        
        # Generate response
        try:
            response = self.llm(messages)
            return response.content if hasattr(response, 'content') else str(response)
        except Exception as e:
            self.logger.error(f"LLM generation failed: {str(e)}")
            return "I apologize, but I'm experiencing technical difficulties. Please try again later."
    
    def _determine_response_type(self, action: str) -> ResponseType:
        """Determine response type based on action"""
        type_mapping = {
            'get_leave_balance': ResponseType.INFORMATION,
            'process_payslip_request': ResponseType.VALIDATION_REQUEST,
            'process_form16_request': ResponseType.VALIDATION_REQUEST,
            'provide_policy_information': ResponseType.INFORMATION,
            'general_hr_assistance': ResponseType.GUIDANCE
        }
        return type_mapping.get(action, ResponseType.INFORMATION)
    
    def _detect_language(self, text: str) -> str:
        """Detect language of user input"""
        # Simple language detection - can be enhanced with proper language detection library
        hindi_chars = any('\u0900' <= char <= '\u097F' for char in text)
        if hindi_chars:
            return 'hindi'
        return 'english'
    
    def _prepare_response_data(
        self,
        llm_response: str,
        tool_results: Dict[str, Any],
        hr_decision
    ) -> Dict[str, Any]:
        """Prepare data for response formatting"""
        
        return {
            'content': llm_response,
            'tool_results': tool_results,
            'action_taken': hr_decision.action,
            'confidence': hr_decision.confidence.value if hasattr(hr_decision.confidence, 'value') else 'high',
            'reasoning': hr_decision.reasoning,
            'current_date': datetime.now().strftime('%B %d, %Y')
        }
    
    async def _log_conversation(
        self,
        session_id: str,
        emp_code: str,
        user_question: str,
        bot_response: str,
        processing_time: float,
        user_ip: str
    ):
        """Log conversation for analytics and audit"""
        
        try:
            # Create conversation log
            conversation_log = ConversationLog(
                session_id=session_id,
                emp_id=emp_code,
                user_message=user_question,
                bot_response=bot_response,
                response_time=processing_time
            )
            
            # Store in database (implement based on your DB structure)
            # This would integrate with your existing logging system
            
            self.logger.info(f"Conversation logged for session {session_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to log conversation: {str(e)}")
    
    async def _check_autonomous_tasks(
        self,
        decision_context: DecisionContext,
        hr_decision
    ):
        """Check if autonomous tasks should be triggered"""
        
        try:
            # Example: Schedule document delivery if needed
            if hr_decision.action in ['process_payslip_request', 'process_form16_request']:
                # This would be triggered after validation is complete
                pass
            
            # Example: Schedule compliance monitoring
            if decision_context.scenario_type == HRScenarioType.ATTENDANCE_ISSUE:
                compliance_task = AutonomousTask(
                    task_id=f"compliance_check_{decision_context.employee_id}_{datetime.now().strftime('%Y%m%d')}",
                    task_type="attendance_compliance",
                    description=f"Compliance check for employee {decision_context.employee_id}",
                    priority=TaskPriority.MEDIUM,
                    scheduled_time=datetime.now() + timedelta(hours=1),
                    employee_id=decision_context.employee_id
                )
                
                await self.agentic_orchestrator.schedule_task(compliance_task)
                
        except Exception as e:
            self.logger.error(f"Error checking autonomous tasks: {str(e)}")
    
    async def start_autonomous_monitoring(self):
        """Start autonomous monitoring processes"""
        try:
            await self.agentic_orchestrator.start_proactive_monitoring()
            self.logger.info("Autonomous monitoring started")
        except Exception as e:
            self.logger.error(f"Failed to start autonomous monitoring: {str(e)}")
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health"""
        return {
            'enhanced_bot_status': 'active',
            'decision_engine_status': 'active',
            'agentic_system_health': self.agentic_orchestrator.get_system_health(),
            'active_sessions': len(self.active_sessions),
            'last_health_check': datetime.now().isoformat()
        }
